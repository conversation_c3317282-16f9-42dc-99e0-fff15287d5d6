import React, { useState } from 'react';
import {
  Building2,
  Users,
  FileText,
  Calendar,
  // DollarSign,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Search,
  Filter,
  Download,
  Upload,
  Eye,
  Edit3,
  Trash2,
  Bell,
  Settings,
  LogOut,
  User,
  MapPin,
  Phone,
  Mail,
  Globe,
  Hash,
  Clock,
  TrendingUp,
  // BarChart3,
  FileCheck,
  AlertCircle,
  Plus
} from 'lucide-react';
import type { structure } from './Interface/interface';

function App() {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('All');

  // Date formatting utility functions
  const formatDateToDisplay = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString; // Return original if invalid

    const day = date.getDate().toString().padStart(2, '0');
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    return `${day}-${month}-${year}`;
  };

  const formatDateToInput = (displayDate) => {
    if (!displayDate) return '';
    // Convert "15-Dec-2023" to "2023-12-15" for HTML date input
    const parts = displayDate.split('-');
    if (parts.length !== 3) return '';

    const day = parts[0];
    const monthName = parts[1];
    const year = parts[2];

    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const monthIndex = months.indexOf(monthName);
    if (monthIndex === -1) return '';

    const month = (monthIndex + 1).toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const getCurrentDateFormatted = () => {
    return formatDateToDisplay(new Date().toISOString());
  };

  // File utility functions
  const getFileExtension = (filename) => {
    return filename.split('.').pop().toLowerCase();
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileExtension) => {
    switch (fileExtension.toLowerCase()) {
      case 'pdf':
        return '📄';
      case 'doc':
      case 'docx':
        return '📝';
      case 'xls':
      case 'xlsx':
        return '📊';
      case 'ppt':
      case 'pptx':
        return '📋';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return '🖼️';
      default:
        return '📎';
    }
  };

  const isValidFileType = (file) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'image/jpeg',
      'image/png',
      'image/gif',
      'text/plain'
    ];
    return allowedTypes.includes(file.type);
  };

  // Enhanced download functions
  const downloadFile = async (document) => {
    if (!document.fileUrl) {
      alert('File not available for download');
      return;
    }

    setDownloadingFiles(prev => new Set([...prev, document.id]));

    try {
      // For blob URLs (uploaded files)
      if (document.fileUrl.startsWith('blob:')) {
        const link = document.createElement('a');
        link.href = document.fileUrl;
        link.download = document.originalName || document.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else {
        // For external URLs, fetch and download
        const response = await fetch(document.fileUrl);
        if (!response.ok) throw new Error('Download failed');

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = document.originalName || document.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Download failed:', error);
      alert('Failed to download file. Please try again.');
    } finally {
      setDownloadingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(document.id);
        return newSet;
      });
    }
  };

  const downloadMultipleFiles = async () => {
    if (selectedDocuments.size === 0) {
      alert('Please select documents to download');
      return;
    }

    const documentsToDownload = documents.filter(doc =>
      selectedDocuments.has(doc.id) && doc.fileUrl
    );

    if (documentsToDownload.length === 0) {
      alert('No downloadable files selected');
      return;
    }

    // Download files one by one with a small delay
    for (const doc of documentsToDownload) {
      await downloadFile(doc);
      // Small delay between downloads to prevent browser blocking
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setSelectedDocuments(new Set());
  };

  const toggleDocumentSelection = (documentId) => {
    setSelectedDocuments(prev => {
      const newSet = new Set(prev);
      if (newSet.has(documentId)) {
        newSet.delete(documentId);
      } else {
        newSet.add(documentId);
      }
      return newSet;
    });
  };

  const selectAllDocuments = () => {
    const downloadableDocuments = documents.filter(doc => doc.fileUrl);
    setSelectedDocuments(new Set(downloadableDocuments.map(doc => doc.id)));
  };

  const clearSelection = () => {
    setSelectedDocuments(new Set());
  };

  // Modal and form states
  const [showDirectorModal, setShowDirectorModal] = useState(false);
  const [editingDirector, setEditingDirector] = useState(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [directorToDelete, setDirectorToDelete] = useState(null);

  // Document modal states
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [editingDocument, setEditingDocument] = useState(null);
  const [showDocumentDeleteConfirm, setShowDocumentDeleteConfirm] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);

  // Compliance modal states
  const [showComplianceModal, setShowComplianceModal] = useState(false);
  const [editingCompliance, setEditingCompliance] = useState(null);
  const [showComplianceDeleteConfirm, setShowComplianceDeleteConfirm] = useState(false);
  const [complianceToDelete, setComplianceToDelete] = useState(null);

  // Download states
  const [downloadingFiles, setDownloadingFiles] = useState(new Set());
  const [selectedDocuments, setSelectedDocuments] = useState(new Set());

  // Sample data
  const company = {
    cin: 'U82990KA2023PTC182604',
    name: 'Fractioned Technologies and Business Management Private Limited',
    status: 'Active',
    category: 'Company limited by shares',
    subcategory: 'Non-govt company',
    registrationDate: '15-Dec-2023',
    authorizedCapital: '₹10,00,000',
    paidupCapital: '₹1,00,000',
    registrarOffice: 'Karnataka',
    address: {
      street: '123, Technology Park, Electronic City',
      city: 'Bangalore',
      state: 'Karnataka',
      pincode: '560100'
    },
    contact: {
      phone: '+91-80-1234-5678',
      email: '<EMAIL>',
      website: 'www.fractionedtech.com'
    }
  };

  const [directors, setDirectors] = useState([
    {
      id: '1',
      name: 'Rajesh Kumar Sharma',
      din: '08765432',
      designation: 'Managing Director',
      appointmentDate: '15-Dec-2023',
      status: 'Active'
    },
    {
      id: '2',
      name: 'Priya Srinivasan',
      din: '09876543',
      designation: 'Director',
      appointmentDate: '15-Dec-2023',
      status: 'Active'
    },
    {
      id: '3',
      name: 'Amit Patel',
      din: '07654321',
      designation: 'Independent Director',
      appointmentDate: '20-Jan-2024',
      status: 'Active'
    }
  ]);

  // CRUD functions for directors
  const addDirector = (directorData) => {
    const newDirector = {
      ...directorData,
      id: Date.now().toString(), // Simple ID generation
      status: 'Active'
    };
    setDirectors([...directors, newDirector]);
    setShowDirectorModal(false);
    setEditingDirector(null);
  };

  const updateDirector = (directorData) => {
    setDirectors(directors.map(director =>
      director.id === editingDirector.id
        ? { ...director, ...directorData }
        : director
    ));
    setShowDirectorModal(false);
    setEditingDirector(null);
  };

  const deleteDirector = (directorId) => {
    setDirectors(directors.filter(director => director.id !== directorId));
    setShowDeleteConfirm(false);
    setDirectorToDelete(null);
  };

  const handleEditDirector = (director) => {
    setEditingDirector(director);
    setShowDirectorModal(true);
  };

  const handleDeleteDirector = (director) => {
    setDirectorToDelete(director);
    setShowDeleteConfirm(true);
  };

  const [documents, setDocuments] = useState([
    {
      id: '1',
      name: 'Annual Return FY 2023-24.pdf',
      originalName: 'Annual Return FY 2023-24.pdf',
      type: 'Annual Return',
      uploadDate: '15-Sep-2024',
      dueDate: '30-Sep-2024',
      status: 'Filed',
      fileSize: '2.4 MB',
      fileType: 'application/pdf',
      fileExtension: 'pdf',
      fileUrl: null, // In a real app, this would be a URL to the stored file
      file: null // For demo purposes
    },
    {
      id: '2',
      name: 'Financial Statement FY 2023-24.pdf',
      originalName: 'Financial Statement FY 2023-24.pdf',
      type: 'Financial Statement',
      uploadDate: '10-Aug-2024',
      dueDate: '30-Aug-2024',
      status: 'Filed',
      fileSize: '5.1 MB',
      fileType: 'application/pdf',
      fileExtension: 'pdf',
      fileUrl: null,
      file: null
    },
    {
      id: '3',
      name: 'Board Resolution - Director Appointment.docx',
      originalName: 'Board Resolution - Director Appointment.docx',
      type: 'Board Resolution',
      uploadDate: '22-Jan-2024',
      dueDate: '30-Jan-2024',
      status: 'Filed',
      fileSize: '1.2 MB',
      fileType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      fileExtension: 'docx',
      fileUrl: null,
      file: null
    },
    {
      id: '4',
      name: 'Audit Report FY 2024-25',
      originalName: '',
      type: 'Audit Report',
      uploadDate: '',
      dueDate: '30-Sep-2025',
      status: 'Pending',
      fileSize: '',
      fileType: '',
      fileExtension: '',
      fileUrl: null,
      file: null
    },
    {
      id: '5',
      name: 'Income Tax Return FY 2023-24',
      originalName: '',
      type: 'Tax Filing',
      uploadDate: '',
      dueDate: '31-Oct-2024',
      status: 'Overdue',
      fileSize: '',
      fileType: '',
      fileExtension: '',
      fileUrl: null,
      file: null
    }
  ]);

  // CRUD functions for documents
  const addDocument = (documentData) => {
    const file = documentData.file;
    const newDocument = {
      ...documentData,
      id: Date.now().toString(),
      uploadDate: getCurrentDateFormatted(),
      name: file ? file.name : documentData.name,
      originalName: file ? file.name : documentData.name,
      fileSize: file ? formatFileSize(file.size) : '0 KB',
      fileType: file ? file.type : '',
      fileExtension: file ? getFileExtension(file.name) : '',
      fileUrl: file ? URL.createObjectURL(file) : null,
      file: file,
      status: file ? 'Filed' : 'Pending'
    };
    setDocuments([...documents, newDocument]);
    setShowDocumentModal(false);
    setEditingDocument(null);
  };

  const updateDocument = (documentData) => {
    setDocuments(documents.map(document =>
      document.id === editingDocument.id
        ? { ...document, ...documentData }
        : document
    ));
    setShowDocumentModal(false);
    setEditingDocument(null);
  };

  const deleteDocument = (documentId) => {
    setDocuments(documents.filter(document => document.id !== documentId));
    setShowDocumentDeleteConfirm(false);
    setDocumentToDelete(null);
  };

  const handleEditDocument = (document) => {
    setEditingDocument(document);
    setShowDocumentModal(true);
  };

  const handleDeleteDocument = (document) => {
    setDocumentToDelete(document);
    setShowDocumentDeleteConfirm(true);
  };

  // CRUD functions for compliance items
  const addCompliance = (complianceData) => {
    const newCompliance = {
      ...complianceData,
      id: Date.now().toString(),
      dueDate: formatDateToDisplay(complianceData.dueDate)
    };
    setComplianceItems([...complianceItems, newCompliance]);
    setShowComplianceModal(false);
    setEditingCompliance(null);
  };

  const updateCompliance = (complianceData) => {
    setComplianceItems(complianceItems.map(compliance =>
      compliance.id === editingCompliance.id
        ? { ...compliance, ...complianceData, dueDate: formatDateToDisplay(complianceData.dueDate) }
        : compliance
    ));
    setShowComplianceModal(false);
    setEditingCompliance(null);
  };

  const deleteCompliance = (complianceId) => {
    setComplianceItems(complianceItems.filter(compliance => compliance.id !== complianceId));
    setShowComplianceDeleteConfirm(false);
    setComplianceToDelete(null);
  };

  const handleEditCompliance = (compliance) => {
    setEditingCompliance(compliance);
    setShowComplianceModal(true);
  };

  const handleDeleteCompliance = (compliance) => {
    setComplianceToDelete(compliance);
    setShowComplianceDeleteConfirm(true);
  };

  const [complianceItems, setComplianceItems] = useState([
    {
      id: '1',
      name: 'Annual Return Filing',
      dueDate: '30-Sep-2024',
      status: 'Compliant',
      priority: 'High',
      description: 'Form MGT-7 filed successfully'
    },
    {
      id: '2',
      name: 'Board Meeting Minutes',
      dueDate: '15-Nov-2024',
      status: 'Pending',
      priority: 'Medium',
      description: 'Quarterly board meeting documentation pending'
    },
    {
      id: '3',
      name: 'Income Tax Return',
      dueDate: '31-Oct-2024',
      status: 'Overdue',
      priority: 'High',
      description: 'ITR filing is overdue - immediate action required'
    },
    {
      id: '4',
      name: 'TDS Return Filing',
      dueDate: '07-Dec-2024',
      status: 'Pending',
      priority: 'Medium',
      description: 'Monthly TDS return for November'
    },
    {
      id: '5',
      name: 'Statutory Audit',
      dueDate: '30-Sep-2025',
      status: 'Compliant',
      priority: 'High',
      description: 'Annual statutory audit completed'
    }
  ]);

  // Pending tasks data - combining pending compliance items and documents
  const pendingTasks = [
    ...complianceItems.filter(item => item.status === 'Pending' || item.status === 'Overdue'),
    ...documents.filter(doc => doc.status === 'Pending' || doc.status === 'Overdue').map(doc => ({
      id: `doc-${doc.id}`,
      name: doc.name,
      dueDate: doc.dueDate,
      status: doc.status,
      priority: doc.status === 'Overdue' ? 'High' : 'Medium',
      description: `Document: ${doc.type}`,
      type: 'document'
    }))
  ].map(task => ({
    ...task,
    type: task.type || 'compliance'
  }));

  const getStatusColor = (status) => {
    switch (status) {
      case 'Compliant':
      case 'Filed':
      case 'Active':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'Pending':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'Overdue':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Compliant':
      case 'Filed':
      case 'Active':
        return <CheckCircle className="w-4 h-4" />;
      case 'Pending':
        return <Clock className="w-4 h-4" />;
      case 'Overdue':
        return <XCircle className="w-4 h-4" />;
      default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'High':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'Medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'Low':
        return 'text-green-600 bg-green-50 border-green-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.type.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'All' || doc.type === filterType;
    return matchesSearch && matchesFilter;
  });

  const overdueCount = complianceItems.filter(item => item.status === 'Overdue').length;
  const pendingCount = complianceItems.filter(item => item.status === 'Pending').length;
  const compliantCount = complianceItems.filter(item => item.status === 'Compliant').length;

  // Director Modal Component
  const DirectorModal = () => {
    const [formData, setFormData] = useState({
      name: '',
      din: '',
      designation: '',
      appointmentDate: ''
    });
    const [errors, setErrors] = useState({});

    // Update form data when editingDirector changes
    React.useEffect(() => {
      if (editingDirector) {
        setFormData({
          name: editingDirector.name || '',
          din: editingDirector.din || '',
          designation: editingDirector.designation || '',
          appointmentDate: formatDateToInput(editingDirector.appointmentDate) || ''
        });
      } else {
        setFormData({
          name: '',
          din: '',
          designation: '',
          appointmentDate: ''
        });
      }
      setErrors({});
    }, [editingDirector]);

    const validateForm = () => {
      const newErrors = {};
      if (!formData.name.trim()) newErrors.name = 'Name is required';
      if (!formData.din.trim()) newErrors.din = 'DIN is required';
      if (!/^\d{8}$/.test(formData.din)) newErrors.din = 'DIN must be 8 digits';
      if (!formData.designation.trim()) newErrors.designation = 'Designation is required';
      if (!formData.appointmentDate.trim()) newErrors.appointmentDate = 'Appointment date is required';

      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e) => {
      e.preventDefault();
      if (validateForm()) {
        const formattedData = {
          ...formData,
          appointmentDate: formatDateToDisplay(formData.appointmentDate)
        };
        if (editingDirector) {
          updateDirector(formattedData);
        } else {
          addDirector(formattedData);
        }
      }
    };

    const handleClose = () => {
      setShowDirectorModal(false);
      setEditingDirector(null);
      setFormData({ name: '', din: '', designation: '', appointmentDate: '' });
      setErrors({});
    };

    if (!showDirectorModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              {editingDirector ? 'Edit Director' : 'Add New Director'}
            </h3>
          </div>
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Full Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter full name"
              />
              {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                DIN (Director Identification Number) *
              </label>
              <input
                type="text"
                value={formData.din}
                onChange={(e) => setFormData({ ...formData, din: e.target.value })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.din ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="8-digit DIN"
                maxLength="8"
              />
              {errors.din && <p className="text-red-500 text-xs mt-1">{errors.din}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Designation *
              </label>
              <select
                value={formData.designation}
                onChange={(e) => setFormData({ ...formData, designation: e.target.value })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.designation ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select designation</option>
                <option value="Managing Director">Managing Director</option>
                <option value="Director">Director</option>
                <option value="Independent Director">Independent Director</option>
                <option value="Executive Director">Executive Director</option>
                <option value="Non-Executive Director">Non-Executive Director</option>
              </select>
              {errors.designation && <p className="text-red-500 text-xs mt-1">{errors.designation}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Appointment Date *
              </label>
              <input
                type="date"
                value={formData.appointmentDate}
                onChange={(e) => setFormData({ ...formData, appointmentDate: e.target.value })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.appointmentDate ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.appointmentDate && <p className="text-red-500 text-xs mt-1">{errors.appointmentDate}</p>}
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {editingDirector ? 'Update' : 'Add'} Director
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  // Delete Confirmation Modal Component
  const DeleteConfirmModal = () => {
    if (!showDeleteConfirm || !directorToDelete) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
          <div className="p-6">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-red-100 rounded-full">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Delete Director</h3>
                <p className="text-gray-600">This action cannot be undone.</p>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-700">
                Are you sure you want to delete <strong>{directorToDelete.name}</strong>?
              </p>
              <p className="text-xs text-gray-500 mt-1">
                DIN: {directorToDelete.din} • {directorToDelete.designation}
              </p>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setDirectorToDelete(null);
                }}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => deleteDirector(directorToDelete.id)}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Delete Director
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Document Modal Component
  const DocumentModal = () => {
    const [formData, setFormData] = useState({
      name: '',
      type: '',
      dueDate: '',
      status: 'Pending',
      file: null
    });
    const [errors, setErrors] = useState({});
    const [dragActive, setDragActive] = useState(false);

    // Update form data when editingDocument changes
    React.useEffect(() => {
      if (editingDocument) {
        setFormData({
          name: editingDocument.originalName || editingDocument.name || '',
          type: editingDocument.type || '',
          dueDate: formatDateToInput(editingDocument.dueDate) || '',
          status: editingDocument.status || 'Pending',
          file: editingDocument.file || null
        });
      } else {
        setFormData({
          name: '',
          type: '',
          dueDate: '',
          status: 'Pending',
          file: null
        });
      }
      setErrors({});
    }, [editingDocument]);

    const handleFileSelect = (file) => {
      if (!isValidFileType(file)) {
        setErrors({ file: 'Invalid file type. Please upload PDF, Word, Excel, PowerPoint, or image files.' });
        return;
      }

      if (file.size > 50 * 1024 * 1024) { // 50MB limit
        setErrors({ file: 'File size must be less than 50MB.' });
        return;
      }

      setFormData({ ...formData, file, name: file.name });
      setErrors({ ...errors, file: undefined });
    };

    const handleDrop = (e) => {
      e.preventDefault();
      setDragActive(false);
      const files = e.dataTransfer.files;
      if (files && files[0]) {
        handleFileSelect(files[0]);
      }
    };

    const handleDragOver = (e) => {
      e.preventDefault();
      setDragActive(true);
    };

    const handleDragLeave = (e) => {
      e.preventDefault();
      setDragActive(false);
    };

    const validateForm = () => {
      const newErrors = {};
      if (!editingDocument && !formData.file) newErrors.file = 'Please select a file to upload';
      if (!formData.type.trim()) newErrors.type = 'Document type is required';
      if (!formData.dueDate.trim()) newErrors.dueDate = 'Due date is required';

      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e) => {
      e.preventDefault();
      if (validateForm()) {
        const formattedData = {
          ...formData,
          dueDate: formatDateToDisplay(formData.dueDate)
        };
        if (editingDocument) {
          updateDocument(formattedData);
        } else {
          addDocument(formattedData);
        }
      }
    };

    const handleClose = () => {
      setShowDocumentModal(false);
      setEditingDocument(null);
      setFormData({ name: '', type: '', dueDate: '', status: 'Pending', file: null });
      setErrors({});
      setDragActive(false);
    };

    if (!showDocumentModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              {editingDocument ? 'Edit Document' : 'Add New Document'}
            </h3>
          </div>
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            {/* File Upload Section */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {editingDocument ? 'Current File' : 'Upload Document *'}
              </label>

              {editingDocument && editingDocument.file ? (
                <div className="mb-4 p-3 bg-gray-50 rounded-lg border">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{getFileIcon(editingDocument.fileExtension)}</span>
                    <div>
                      <p className="font-medium text-gray-900">{editingDocument.originalName}</p>
                      <p className="text-sm text-gray-600">{editingDocument.fileSize}</p>
                    </div>
                  </div>
                </div>
              ) : null}

              {!editingDocument && (
                <div
                  className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                    dragActive
                      ? 'border-blue-500 bg-blue-50'
                      : errors.file
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-300 hover:border-gray-400'
                  }`}
                  onDrop={handleDrop}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                >
                  {formData.file ? (
                    <div className="flex items-center justify-center gap-3">
                      <span className="text-3xl">{getFileIcon(getFileExtension(formData.file.name))}</span>
                      <div>
                        <p className="font-medium text-gray-900">{formData.file.name}</p>
                        <p className="text-sm text-gray-600">{formatFileSize(formData.file.size)}</p>
                      </div>
                      <button
                        type="button"
                        onClick={() => setFormData({ ...formData, file: null, name: '' })}
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  ) : (
                    <div>
                      <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-2">Drag and drop your file here, or</p>
                      <label className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg cursor-pointer hover:bg-blue-700 transition-colors">
                        Choose File
                        <input
                          type="file"
                          className="hidden"
                          onChange={(e) => e.target.files[0] && handleFileSelect(e.target.files[0])}
                          accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.gif,.txt"
                        />
                      </label>
                      <p className="text-xs text-gray-500 mt-2">
                        Supported formats: PDF, Word, Excel, PowerPoint, Images (Max 50MB)
                      </p>
                    </div>
                  )}
                </div>
              )}
              {errors.file && <p className="text-red-500 text-xs mt-1">{errors.file}</p>}
            </div>

            {/* Document Name (auto-filled from file name) */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Document Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Document name (auto-filled from file)"
                readOnly={!editingDocument}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Document Type *
              </label>
              <select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.type ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select document type</option>
                <option value="Annual Return">Annual Return</option>
                <option value="Financial Statement">Financial Statement</option>
                <option value="Board Resolution">Board Resolution</option>
                <option value="Audit Report">Audit Report</option>
                <option value="Tax Filing">Tax Filing</option>
                <option value="Compliance Certificate">Compliance Certificate</option>
              </select>
              {errors.type && <p className="text-red-500 text-xs mt-1">{errors.type}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Due Date *
              </label>
              <input
                type="date"
                value={formData.dueDate}
                onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.dueDate ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.dueDate && <p className="text-red-500 text-xs mt-1">{errors.dueDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="Pending">Pending</option>
                <option value="Filed">Filed</option>
                <option value="Overdue">Overdue</option>
              </select>
            </div>



            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {editingDocument ? 'Update' : 'Add'} Document
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  // Document Delete Confirmation Modal Component
  const DocumentDeleteConfirmModal = () => {
    if (!showDocumentDeleteConfirm || !documentToDelete) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
          <div className="p-6">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-red-100 rounded-full">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Delete Document</h3>
                <p className="text-gray-600">This action cannot be undone.</p>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-700">
                Are you sure you want to delete <strong>{documentToDelete.name}</strong>?
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Type: {documentToDelete.type} • Due: {documentToDelete.dueDate}
              </p>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => {
                  setShowDocumentDeleteConfirm(false);
                  setDocumentToDelete(null);
                }}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => deleteDocument(documentToDelete.id)}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Delete Document
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Compliance Modal Component
  const ComplianceModal = () => {
    const [formData, setFormData] = useState({
      name: '',
      dueDate: '',
      status: 'Pending',
      priority: 'Medium',
      description: ''
    });
    const [errors, setErrors] = useState({});

    // Update form data when editingCompliance changes
    React.useEffect(() => {
      if (editingCompliance) {
        setFormData({
          name: editingCompliance.name || '',
          dueDate: formatDateToInput(editingCompliance.dueDate) || '',
          status: editingCompliance.status || 'Pending',
          priority: editingCompliance.priority || 'Medium',
          description: editingCompliance.description || ''
        });
      } else {
        setFormData({
          name: '',
          dueDate: '',
          status: 'Pending',
          priority: 'Medium',
          description: ''
        });
      }
      setErrors({});
    }, [editingCompliance]);

    const validateForm = () => {
      const newErrors = {};
      if (!formData.name.trim()) newErrors.name = 'Compliance name is required';
      if (!formData.dueDate.trim()) newErrors.dueDate = 'Due date is required';
      if (!formData.description.trim()) newErrors.description = 'Description is required';

      setErrors(newErrors);
      return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = (e) => {
      e.preventDefault();
      if (validateForm()) {
        if (editingCompliance) {
          updateCompliance(formData);
        } else {
          addCompliance(formData);
        }
      }
    };

    const handleClose = () => {
      setShowComplianceModal(false);
      setEditingCompliance(null);
      setFormData({ name: '', dueDate: '', status: 'Pending', priority: 'Medium', description: '' });
      setErrors({});
    };

    if (!showComplianceModal) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">
              {editingCompliance ? 'Edit Compliance Item' : 'Add New Compliance Item'}
            </h3>
          </div>
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Compliance Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.name ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter compliance name"
              />
              {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Due Date *
              </label>
              <input
                type="date"
                value={formData.dueDate}
                onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.dueDate ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.dueDate && <p className="text-red-500 text-xs mt-1">{errors.dueDate}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="Pending">Pending</option>
                <option value="Compliant">Compliant</option>
                <option value="Overdue">Overdue</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Priority
              </label>
              <select
                value={formData.priority}
                onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="Low">Low</option>
                <option value="Medium">Medium</option>
                <option value="High">High</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                rows={3}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.description ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter compliance description"
              />
              {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
            </div>

            <div className="flex gap-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {editingCompliance ? 'Update' : 'Add'} Compliance
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  };

  // Compliance Delete Confirmation Modal Component
  const ComplianceDeleteConfirmModal = () => {
    if (!showComplianceDeleteConfirm || !complianceToDelete) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
          <div className="p-6">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-red-100 rounded-full">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Delete Compliance Item</h3>
                <p className="text-gray-600">This action cannot be undone.</p>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-700">
                Are you sure you want to delete <strong>{complianceToDelete.name}</strong>?
              </p>
              <p className="text-xs text-gray-500 mt-1">
                Due: {complianceToDelete.dueDate} • Priority: {complianceToDelete.priority}
              </p>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => {
                  setShowComplianceDeleteConfirm(false);
                  setComplianceToDelete(null);
                }}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => deleteCompliance(complianceToDelete.id)}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                Delete Compliance
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderOverview = () => (
    <div className="space-y-8">
      {/* Company Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <div className="flex items-start justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">{company.name}</h1>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <Hash className="w-4 h-4" />
                <span className="font-mono">{company.cin}</span>
              </div>
              <div className={`flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium ${getStatusColor(company.status)}`}>
                {getStatusIcon(company.status)}
                {company.status}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <Download className="w-4 h-4" />
              Export Report
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Company Details</h3>
            <div className="space-y-2 text-sm">
              <div><span className="text-gray-600">Category:</span> <span className="font-medium">{company.category}</span></div>
              <div><span className="text-gray-600">Sub-category:</span> <span className="font-medium">{company.subcategory}</span></div>
              <div><span className="text-gray-600">Registration:</span> <span className="font-medium">{company.registrationDate}</span></div>
              <div><span className="text-gray-600">ROC:</span> <span className="font-medium">{company.registrarOffice}</span></div>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Capital Structure</h3>
            <div className="space-y-2 text-sm">
              <div><span className="text-gray-600">Authorized:</span> <span className="font-medium">{company.authorizedCapital}</span></div>
              <div><span className="text-gray-600">Paid-up:</span> <span className="font-medium">{company.paidupCapital}</span></div>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-gray-900 mb-3">Contact Information</h3>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <Phone className="w-3 h-3 text-gray-400" />
                <span>{company.contact.phone}</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail className="w-3 h-3 text-gray-400" />
                <span>{company.contact.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Globe className="w-3 h-3 text-gray-400" />
                <span>{company.contact.website}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Compliance Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Compliance Items</p>
              <p className="text-2xl font-bold text-gray-900">{complianceItems.length}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-lg">
              <FileCheck className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Compliant</p>
              <p className="text-2xl font-bold text-green-600">{compliantCount}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{pendingCount}</p>
            </div>
            <div className="p-3 bg-yellow-50 rounded-lg">
              <Clock className="w-6 h-6 text-yellow-600" />
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overdue</p>
              <p className="text-2xl font-bold text-red-600">{overdueCount}</p>
            </div>
            <div className="p-3 bg-red-50 rounded-lg">
              <AlertCircle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Address Information */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
        <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <MapPin className="w-5 h-5" />
          Registered Address
        </h3>
        <p className="text-gray-700">
          {company.address.street}, {company.address.city}, {company.address.state} - {company.address.pincode}
        </p>
      </div>
    </div>
  );

  const renderDirectors = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Directors & Key Personnel</h2>
        <button
          onClick={() => setShowDirectorModal(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Add Director
        </button>
      </div>
      
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Name</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">DIN</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Designation</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Appointment Date</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Status</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody>
              {directors.map((director) => (
                <tr key={director.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{director.name}</p>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6 font-mono text-sm text-gray-600">{director.din}</td>
                  <td className="py-4 px-6 text-gray-700">{director.designation}</td>
                  <td className="py-4 px-6 text-gray-600">{director.appointmentDate}</td>
                  <td className="py-4 px-6">
                    <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium ${getStatusColor(director.status)}`}>
                      {getStatusIcon(director.status)}
                      {director.status}
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleEditDirector(director)}
                        className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                        title="Edit Director"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteDirector(director)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete Director"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderDocuments = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold text-gray-900">Document Repository</h2>
          {selectedDocuments.size > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">
                {selectedDocuments.size} selected
              </span>
              <button
                onClick={downloadMultipleFiles}
                className="flex items-center gap-1 px-3 py-1 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors"
              >
                <Download className="w-3 h-3" />
                Download Selected
              </button>
              <button
                onClick={clearSelection}
                className="flex items-center gap-1 px-3 py-1 bg-gray-500 text-white text-sm rounded-lg hover:bg-gray-600 transition-colors"
              >
                Clear
              </button>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          {documents.filter(doc => doc.fileUrl).length > 1 && (
            <button
              onClick={selectedDocuments.size === documents.filter(doc => doc.fileUrl).length ? clearSelection : selectAllDocuments}
              className="flex items-center gap-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            >
              {selectedDocuments.size === documents.filter(doc => doc.fileUrl).length ? 'Deselect All' : 'Select All'}
            </button>
          )}
          <button
            onClick={() => setShowDocumentModal(true)}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            Add Document
          </button>
        </div>
      </div>
      
      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search documents..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="All">All Types</option>
              <option value="Annual Return">Annual Return</option>
              <option value="Financial Statement">Financial Statement</option>
              <option value="Board Resolution">Board Resolution</option>
              <option value="Audit Report">Audit Report</option>
              <option value="Tax Filing">Tax Filing</option>
              <option value="Compliance Certificate">Compliance Certificate</option>
            </select>
          </div>
        </div>
      </div>
      
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="text-left py-3 px-6 font-semibold text-gray-900 w-12">
                  <input
                    type="checkbox"
                    checked={selectedDocuments.size > 0 && selectedDocuments.size === documents.filter(doc => doc.fileUrl).length}
                    onChange={selectedDocuments.size === documents.filter(doc => doc.fileUrl).length ? clearSelection : selectAllDocuments}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Document Name</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Type</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Upload Date</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Due Date</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Status</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Size</th>
                <th className="text-left py-3 px-6 font-semibold text-gray-900">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredDocuments.map((doc) => (
                <tr key={doc.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-4 px-6">
                    {doc.fileUrl && (
                      <input
                        type="checkbox"
                        checked={selectedDocuments.has(doc.id)}
                        onChange={() => toggleDocumentSelection(doc.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    )}
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-50 rounded-lg">
                        <span className="text-lg">{doc.fileExtension ? getFileIcon(doc.fileExtension) : '📎'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-900 block">{doc.name}</span>
                        {doc.fileExtension && (
                          <span className="text-xs text-gray-500 uppercase">{doc.fileExtension}</span>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6 text-gray-600">{doc.type}</td>
                  <td className="py-4 px-6 text-gray-600">{doc.uploadDate || '-'}</td>
                  <td className="py-4 px-6 text-gray-600">{doc.dueDate}</td>
                  <td className="py-4 px-6">
                    <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium ${getStatusColor(doc.status)}`}>
                      {getStatusIcon(doc.status)}
                      {doc.status}
                    </div>
                  </td>
                  <td className="py-4 px-6 text-gray-600">{doc.fileSize || '-'}</td>
                  <td className="py-4 px-6">
                    <div className="flex items-center gap-2">
                      {doc.fileUrl && (
                        <button
                          onClick={() => window.open(doc.fileUrl, '_blank')}
                          className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                          title="Preview File"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                      )}
                      {doc.fileUrl && (
                        <button
                          onClick={() => downloadFile(doc)}
                          disabled={downloadingFiles.has(doc.id)}
                          className={`p-1 transition-colors ${
                            downloadingFiles.has(doc.id)
                              ? 'text-blue-500 cursor-not-allowed'
                              : 'text-gray-400 hover:text-green-600'
                          }`}
                          title={downloadingFiles.has(doc.id) ? "Downloading..." : "Download File"}
                        >
                          {downloadingFiles.has(doc.id) ? (
                            <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                          ) : (
                            <Download className="w-4 h-4" />
                          )}
                        </button>
                      )}
                      <button
                        onClick={() => handleEditDocument(doc)}
                        className="p-1 text-gray-400 hover:text-yellow-600 transition-colors"
                        title="Edit Document"
                      >
                        <Edit3 className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteDocument(doc)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete Document"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderCompliance = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Compliance Tracker</h2>
        <button
          onClick={() => setShowComplianceModal(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4" />
          Add Compliance Item
        </button>
      </div>
      
      {/* Compliance Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">Compliance Rate</h3>
            <TrendingUp className="w-5 h-5 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-green-600 mb-2">
            {Math.round((compliantCount / complianceItems.length) * 100)}%
          </div>
          <p className="text-sm text-gray-600">{compliantCount} of {complianceItems.length} items compliant</p>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">Pending Actions</h3>
            <Clock className="w-5 h-5 text-yellow-600" />
          </div>
          <div className="text-2xl font-bold text-yellow-600 mb-2">{pendingCount}</div>
          <p className="text-sm text-gray-600">Items requiring attention</p>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">Overdue Items</h3>
            <AlertCircle className="w-5 h-5 text-red-600" />
          </div>
          <div className="text-2xl font-bold text-red-600 mb-2">{overdueCount}</div>
          <p className="text-sm text-gray-600">Immediate action required</p>
        </div>
      </div>
      
      {/* Compliance Items List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900">Compliance Items</h3>
        </div>
        <div className="divide-y divide-gray-100">
          {complianceItems.map((item) => (
            <div key={item.id} className="p-6 hover:bg-gray-50">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h4 className="font-medium text-gray-900">{item.name}</h4>
                    <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium ${getStatusColor(item.status)}`}>
                      {getStatusIcon(item.status)}
                      {item.status}
                    </div>
                    <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full border text-xs font-medium ${getPriorityColor(item.priority)}`}>
                      {item.priority}
                    </div>
                  </div>
                  <p className="text-gray-600 mb-2">{item.description}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      Due: {item.dueDate}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-2 ml-4">
                  <button
                    onClick={() => handleEditCompliance(item)}
                    className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                    title="Edit Compliance"
                  >
                    <Edit3 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteCompliance(item)}
                    className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                    title="Delete Compliance"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );



  const renderPendingTasks = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Pending Tasks</h2>
        <div className="flex items-center gap-3">
          <span className="text-sm text-gray-600">
            {pendingTasks.length} tasks requiring attention
          </span>
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Plus className="w-4 h-4" />
            Add Task
          </button>
        </div>
      </div>

      {/* Task Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">Total Pending</h3>
            <Clock className="w-5 h-5 text-yellow-600" />
          </div>
          <div className="text-2xl font-bold text-yellow-600 mb-2">
            {pendingTasks.filter(task => task.status === 'Pending').length}
          </div>
          <p className="text-sm text-gray-600">Tasks in progress</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">Overdue</h3>
            <AlertCircle className="w-5 h-5 text-red-600" />
          </div>
          <div className="text-2xl font-bold text-red-600 mb-2">
            {pendingTasks.filter(task => task.status === 'Overdue').length}
          </div>
          <p className="text-sm text-gray-600">Immediate attention required</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-900">High Priority</h3>
            <AlertTriangle className="w-5 h-5 text-orange-600" />
          </div>
          <div className="text-2xl font-bold text-orange-600 mb-2">
            {pendingTasks.filter(task => task.priority === 'High').length}
          </div>
          <p className="text-sm text-gray-600">Critical tasks</p>
        </div>
      </div>

      {/* Pending Tasks List */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="font-semibold text-gray-900">All Pending Tasks</h3>
        </div>
        <div className="divide-y divide-gray-100">
          {pendingTasks.length === 0 ? (
            <div className="p-8 text-center">
              <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
              <p className="text-gray-600">No pending tasks at the moment.</p>
            </div>
          ) : (
            pendingTasks.map((task) => (
              <div key={task.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="flex items-center gap-2">
                        {task.type === 'document' ? (
                          <FileText className="w-4 h-4 text-blue-600" />
                        ) : (
                          <FileCheck className="w-4 h-4 text-green-600" />
                        )}
                        <h4 className="font-medium text-gray-900">{task.name}</h4>
                      </div>
                      <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full border text-sm font-medium ${getStatusColor(task.status)}`}>
                        {getStatusIcon(task.status)}
                        {task.status}
                      </div>
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full border text-xs font-medium ${getPriorityColor(task.priority)}`}>
                        {task.priority}
                      </div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        task.type === 'document'
                          ? 'bg-blue-50 text-blue-700 border border-blue-200'
                          : 'bg-green-50 text-green-700 border border-green-200'
                      }`}>
                        {task.type === 'document' ? 'Document' : 'Compliance'}
                      </span>
                    </div>
                    <p className="text-gray-600 mb-2">{task.description}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        Due: {task.dueDate}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                      <Edit3 className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                      <CheckCircle className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'directors':
        return renderDirectors();
      case 'documents':
        return renderDocuments();
      case 'compliance':
        return renderCompliance();
      case 'pending-tasks':
        return renderPendingTasks();
      default:
        return renderOverview();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <Building2 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">ComplianceHub</h1>
                <p className="text-sm text-gray-600">Business Compliance Management</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors relative">
                <Bell className="w-5 h-5" />
                {overdueCount > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {overdueCount}
                  </span>
                )}
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <Settings className="w-5 h-5" />
              </button>
              <div className="flex items-center gap-3 pl-4 border-l border-gray-200">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-blue-600" />
                </div>
                <div className="text-sm">
                  <p className="font-medium text-gray-900">Admin User</p>
                  <p className="text-gray-600">Compliance Officer</p>
                </div>
                <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav className="flex space-x-8 py-4">
          {[
            { id: 'overview', label: 'Company Overview', icon: Building2 },
            { id: 'directors', label: 'Directors', icon: Users },
            { id: 'documents', label: 'Documents', icon: FileText },
            { id: 'compliance', label: 'Compliance', icon: CheckCircle },
            { id: 'pending-tasks', label: 'Pending Tasks', icon: Clock }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-100 text-blue-700 border-2 border-blue-200'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-12">
        {renderContent()}
      </main>

      {/* Modals */}
      <DirectorModal />
      <DeleteConfirmModal />
      <DocumentModal />
      <DocumentDeleteConfirmModal />
      <ComplianceModal />
      <ComplianceDeleteConfirmModal />
    </div>
  );
}

export default App;