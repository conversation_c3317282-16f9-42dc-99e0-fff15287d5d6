{"version": 3, "sources": ["../../src/useMutationState.ts"], "sourcesContent": ["'use client'\nimport * as React from 'react'\n\nimport { notify<PERSON><PERSON><PERSON>, replaceEqual<PERSON>eep } from '@tanstack/query-core'\nimport { useQueryClient } from './QueryClientProvider'\nimport type {\n  Mutation,\n  MutationCache,\n  MutationFilters,\n  MutationState,\n  QueryClient,\n} from '@tanstack/query-core'\n\nexport function useIsMutating(\n  filters?: MutationFilters,\n  queryClient?: QueryClient,\n): number {\n  const client = useQueryClient(queryClient)\n  return useMutationState(\n    { filters: { ...filters, status: 'pending' } },\n    client,\n  ).length\n}\n\ntype MutationStateOptions<TResult = MutationState> = {\n  filters?: MutationFilters\n  select?: (mutation: Mutation) => TResult\n}\n\nfunction getResult<TResult = MutationState>(\n  mutationCache: MutationCache,\n  options: MutationStateOptions<TResult>,\n): Array<TResult> {\n  return mutationCache\n    .findAll(options.filters)\n    .map(\n      (mutation): TResult =>\n        (options.select ? options.select(mutation) : mutation.state) as TResult,\n    )\n}\n\nexport function useMutationState<TResult = MutationState>(\n  options: MutationStateOptions<TResult> = {},\n  queryClient?: QueryClient,\n): Array<TResult> {\n  const mutationCache = useQueryClient(queryClient).getMutationCache()\n  const optionsRef = React.useRef(options)\n  const result = React.useRef<Array<TResult>>(null)\n  if (!result.current) {\n    result.current = getResult(mutationCache, options)\n  }\n\n  React.useEffect(() => {\n    optionsRef.current = options\n  })\n\n  return React.useSyncExternalStore(\n    React.useCallback(\n      (onStoreChange) =>\n        mutationCache.subscribe(() => {\n          const nextResult = replaceEqualDeep(\n            result.current,\n            getResult(mutationCache, optionsRef.current),\n          )\n          if (result.current !== nextResult) {\n            result.current = nextResult\n            notifyManager.schedule(onStoreChange)\n          }\n        }),\n      [mutationCache],\n    ),\n    () => result.current,\n    () => result.current,\n  )!\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,YAAuB;AAEvB,wBAAgD;AAChD,iCAA+B;AASxB,SAAS,cACd,SACA,aACQ;AACR,QAAM,aAAS,2CAAe,WAAW;AACzC,SAAO;AAAA,IACL,EAAE,SAAS,EAAE,GAAG,SAAS,QAAQ,UAAU,EAAE;AAAA,IAC7C;AAAA,EACF,EAAE;AACJ;AAOA,SAAS,UACP,eACA,SACgB;AAChB,SAAO,cACJ,QAAQ,QAAQ,OAAO,EACvB;AAAA,IACC,CAAC,aACE,QAAQ,SAAS,QAAQ,OAAO,QAAQ,IAAI,SAAS;AAAA,EAC1D;AACJ;AAEO,SAAS,iBACd,UAAyC,CAAC,GAC1C,aACgB;AAChB,QAAM,oBAAgB,2CAAe,WAAW,EAAE,iBAAiB;AACnE,QAAM,aAAmB,aAAO,OAAO;AACvC,QAAM,SAAe,aAAuB,IAAI;AAChD,MAAI,CAAC,OAAO,SAAS;AACnB,WAAO,UAAU,UAAU,eAAe,OAAO;AAAA,EACnD;AAEA,EAAM,gBAAU,MAAM;AACpB,eAAW,UAAU;AAAA,EACvB,CAAC;AAED,SAAa;AAAA,IACL;AAAA,MACJ,CAAC,kBACC,cAAc,UAAU,MAAM;AAC5B,cAAM,iBAAa;AAAA,UACjB,OAAO;AAAA,UACP,UAAU,eAAe,WAAW,OAAO;AAAA,QAC7C;AACA,YAAI,OAAO,YAAY,YAAY;AACjC,iBAAO,UAAU;AACjB,0CAAc,SAAS,aAAa;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,MACH,CAAC,aAAa;AAAA,IAChB;AAAA,IACA,MAAM,OAAO;AAAA,IACb,MAAM,OAAO;AAAA,EACf;AACF;", "names": []}