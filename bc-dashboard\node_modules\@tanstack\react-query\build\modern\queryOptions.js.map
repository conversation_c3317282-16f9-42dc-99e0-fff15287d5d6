{"version": 3, "sources": ["../../src/queryOptions.ts"], "sourcesContent": ["import type {\n  DataTag,\n  DefaultError,\n  InitialDataFunction,\n  NonUndefinedGuard,\n  OmitKeyof,\n  QueryFunction,\n  QueryKey,\n  SkipToken,\n} from '@tanstack/query-core'\nimport type { UseQueryOptions } from './types'\n\nexport type UndefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = UseQueryOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  initialData?:\n    | undefined\n    | InitialDataFunction<NonUndefinedGuard<TQueryFnData>>\n    | NonUndefinedGuard<TQueryFnData>\n}\n\nexport type UnusedSkipTokenOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = OmitKeyof<\n  UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  'queryFn'\n> & {\n  queryFn?: Exclude<\n    UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>['queryFn'],\n    SkipToken | undefined\n  >\n}\n\nexport type DefinedInitialDataOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n> = Omit<UseQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'queryFn'> & {\n  initialData:\n    | NonUndefinedGuard<TQueryFnData>\n    | (() => NonUndefinedGuard<TQueryFnData>)\n  queryFn?: QueryFunction<TQueryFnData, TQueryKey>\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n): DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UnusedSkipTokenOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UnusedSkipTokenOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions<\n  TQueryFnData = unknown,\n  TError = DefaultError,\n  TData = TQueryFnData,\n  TQueryKey extends QueryKey = QueryKey,\n>(\n  options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n): UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey> & {\n  queryKey: DataTag<TQueryKey, TQueryFnData, TError>\n}\n\nexport function queryOptions(options: unknown) {\n  return options\n}\n"], "mappings": ";AAoFO,SAAS,aAAa,SAAkB;AAC7C,SAAO;AACT;", "names": []}