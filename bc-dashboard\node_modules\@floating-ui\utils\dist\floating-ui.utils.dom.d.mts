declare function getComputedStyle_2(element: Element): CSSStyleDeclaration;
export { getComputedStyle_2 as getComputedStyle }

export declare function getContainingBlock(element: Element): HTMLElement | null;

export declare function getDocumentElement(node: Node | Window): HTMLElement;

export declare function getFrameElement(win: Window): Element | null;

export declare function getNearestOverflowAncestor(node: Node): HTMLElement;

export declare function getNodeName(node: Node | Window): string;

export declare function getNodeScroll(element: Element | Window): {
    scrollLeft: number;
    scrollTop: number;
};

export declare function getOverflowAncestors(node: Node, list?: OverflowAncestors, traverseIframes?: boolean): OverflowAncestors;

export declare function getParentNode(node: Node): Node;

export declare function getWindow(node: any): typeof window;

export declare function isContainingBlock(elementOrCss: Element | CSSStyleDeclaration): boolean;

export declare function isElement(value: unknown): value is Element;

export declare function isHTMLElement(value: unknown): value is HTMLElement;

export declare function isLastTraversableNode(node: Node): boolean;

export declare function isNode(value: unknown): value is Node;

export declare function isOverflowElement(element: Element): boolean;

export declare function isShadowRoot(value: unknown): value is ShadowRoot;

export declare function isTableElement(element: Element): boolean;

export declare function isTopLayer(element: Element): boolean;

export declare function isWebKit(): boolean;

declare type OverflowAncestors = Array<Element | Window | VisualViewport>;

export { }
