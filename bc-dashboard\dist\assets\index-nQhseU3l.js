function jb(a,o){for(var u=0;u<o.length;u++){const c=o[u];if(typeof c!="string"&&!Array.isArray(c)){for(const s in c)if(s!=="default"&&!(s in a)){const d=Object.getOwnPropertyDescriptor(c,s);d&&Object.defineProperty(a,s,d.get?d:{enumerable:!0,get:()=>c[s]})}}}return Object.freeze(Object.defineProperty(a,Symbol.toStringTag,{value:"Module"}))}(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))c(s);new MutationObserver(s=>{for(const d of s)if(d.type==="childList")for(const h of d.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&c(h)}).observe(document,{childList:!0,subtree:!0});function u(s){const d={};return s.integrity&&(d.integrity=s.integrity),s.referrerPolicy&&(d.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?d.credentials="include":s.crossOrigin==="anonymous"?d.credentials="omit":d.credentials="same-origin",d}function c(s){if(s.ep)return;s.ep=!0;const d=u(s);fetch(s.href,d)}})();function Uv(a){return a&&a.__esModule&&Object.prototype.hasOwnProperty.call(a,"default")?a.default:a}var Vu={exports:{}},Oi={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vh;function zb(){if(Vh)return Oi;Vh=1;var a=Symbol.for("react.transitional.element"),o=Symbol.for("react.fragment");function u(c,s,d){var h=null;if(d!==void 0&&(h=""+d),s.key!==void 0&&(h=""+s.key),"key"in s){d={};for(var v in s)v!=="key"&&(d[v]=s[v])}else d=s;return s=d.ref,{$$typeof:a,type:c,key:h,ref:s!==void 0?s:null,props:d}}return Oi.Fragment=o,Oi.jsx=u,Oi.jsxs=u,Oi}var Xh;function Ub(){return Xh||(Xh=1,Vu.exports=zb()),Vu.exports}var m=Ub(),Xu={exports:{}},ye={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Qh;function Hb(){if(Qh)return ye;Qh=1;var a=Symbol.for("react.transitional.element"),o=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),d=Symbol.for("react.consumer"),h=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),x=Symbol.for("react.lazy"),w=Symbol.iterator;function R(E){return E===null||typeof E!="object"?null:(E=w&&E[w]||E["@@iterator"],typeof E=="function"?E:null)}var N={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},z=Object.assign,T={};function D(E,V,F){this.props=E,this.context=V,this.refs=T,this.updater=F||N}D.prototype.isReactComponent={},D.prototype.setState=function(E,V){if(typeof E!="object"&&typeof E!="function"&&E!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,E,V,"setState")},D.prototype.forceUpdate=function(E){this.updater.enqueueForceUpdate(this,E,"forceUpdate")};function L(){}L.prototype=D.prototype;function k(E,V,F){this.props=E,this.context=V,this.refs=T,this.updater=F||N}var _=k.prototype=new L;_.constructor=k,z(_,D.prototype),_.isPureReactComponent=!0;var Z=Array.isArray,G={H:null,A:null,T:null,S:null,V:null},I=Object.prototype.hasOwnProperty;function $(E,V,F,J,W,he){return F=he.ref,{$$typeof:a,type:E,key:V,ref:F!==void 0?F:null,props:he}}function K(E,V){return $(E.type,V,void 0,void 0,void 0,E.props)}function oe(E){return typeof E=="object"&&E!==null&&E.$$typeof===a}function ve(E){var V={"=":"=0",":":"=2"};return"$"+E.replace(/[=:]/g,function(F){return V[F]})}var pe=/\/+/g;function se(E,V){return typeof E=="object"&&E!==null&&E.key!=null?ve(""+E.key):V.toString(36)}function ge(){}function me(E){switch(E.status){case"fulfilled":return E.value;case"rejected":throw E.reason;default:switch(typeof E.status=="string"?E.then(ge,ge):(E.status="pending",E.then(function(V){E.status==="pending"&&(E.status="fulfilled",E.value=V)},function(V){E.status==="pending"&&(E.status="rejected",E.reason=V)})),E.status){case"fulfilled":return E.value;case"rejected":throw E.reason}}throw E}function fe(E,V,F,J,W){var he=typeof E;(he==="undefined"||he==="boolean")&&(E=null);var ie=!1;if(E===null)ie=!0;else switch(he){case"bigint":case"string":case"number":ie=!0;break;case"object":switch(E.$$typeof){case a:case o:ie=!0;break;case x:return ie=E._init,fe(ie(E._payload),V,F,J,W)}}if(ie)return W=W(E),ie=J===""?"."+se(E,0):J,Z(W)?(F="",ie!=null&&(F=ie.replace(pe,"$&/")+"/"),fe(W,V,F,"",function(_e){return _e})):W!=null&&(oe(W)&&(W=K(W,F+(W.key==null||E&&E.key===W.key?"":(""+W.key).replace(pe,"$&/")+"/")+ie)),V.push(W)),1;ie=0;var P=J===""?".":J+":";if(Z(E))for(var ce=0;ce<E.length;ce++)J=E[ce],he=P+se(J,ce),ie+=fe(J,V,F,he,W);else if(ce=R(E),typeof ce=="function")for(E=ce.call(E),ce=0;!(J=E.next()).done;)J=J.value,he=P+se(J,ce++),ie+=fe(J,V,F,he,W);else if(he==="object"){if(typeof E.then=="function")return fe(me(E),V,F,J,W);throw V=String(E),Error("Objects are not valid as a React child (found: "+(V==="[object Object]"?"object with keys {"+Object.keys(E).join(", ")+"}":V)+"). If you meant to render a collection of children, use an array instead.")}return ie}function M(E,V,F){if(E==null)return E;var J=[],W=0;return fe(E,J,"","",function(he){return V.call(F,he,W++)}),J}function Q(E){if(E._status===-1){var V=E._result;V=V(),V.then(function(F){(E._status===0||E._status===-1)&&(E._status=1,E._result=F)},function(F){(E._status===0||E._status===-1)&&(E._status=2,E._result=F)}),E._status===-1&&(E._status=0,E._result=V)}if(E._status===1)return E._result.default;throw E._result}var B=typeof reportError=="function"?reportError:function(E){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var V=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof E=="object"&&E!==null&&typeof E.message=="string"?String(E.message):String(E),error:E});if(!window.dispatchEvent(V))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",E);return}console.error(E)};function ae(){}return ye.Children={map:M,forEach:function(E,V,F){M(E,function(){V.apply(this,arguments)},F)},count:function(E){var V=0;return M(E,function(){V++}),V},toArray:function(E){return M(E,function(V){return V})||[]},only:function(E){if(!oe(E))throw Error("React.Children.only expected to receive a single React element child.");return E}},ye.Component=D,ye.Fragment=u,ye.Profiler=s,ye.PureComponent=k,ye.StrictMode=c,ye.Suspense=y,ye.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=G,ye.__COMPILER_RUNTIME={__proto__:null,c:function(E){return G.H.useMemoCache(E)}},ye.cache=function(E){return function(){return E.apply(null,arguments)}},ye.cloneElement=function(E,V,F){if(E==null)throw Error("The argument must be a React element, but you passed "+E+".");var J=z({},E.props),W=E.key,he=void 0;if(V!=null)for(ie in V.ref!==void 0&&(he=void 0),V.key!==void 0&&(W=""+V.key),V)!I.call(V,ie)||ie==="key"||ie==="__self"||ie==="__source"||ie==="ref"&&V.ref===void 0||(J[ie]=V[ie]);var ie=arguments.length-2;if(ie===1)J.children=F;else if(1<ie){for(var P=Array(ie),ce=0;ce<ie;ce++)P[ce]=arguments[ce+2];J.children=P}return $(E.type,W,void 0,void 0,he,J)},ye.createContext=function(E){return E={$$typeof:h,_currentValue:E,_currentValue2:E,_threadCount:0,Provider:null,Consumer:null},E.Provider=E,E.Consumer={$$typeof:d,_context:E},E},ye.createElement=function(E,V,F){var J,W={},he=null;if(V!=null)for(J in V.key!==void 0&&(he=""+V.key),V)I.call(V,J)&&J!=="key"&&J!=="__self"&&J!=="__source"&&(W[J]=V[J]);var ie=arguments.length-2;if(ie===1)W.children=F;else if(1<ie){for(var P=Array(ie),ce=0;ce<ie;ce++)P[ce]=arguments[ce+2];W.children=P}if(E&&E.defaultProps)for(J in ie=E.defaultProps,ie)W[J]===void 0&&(W[J]=ie[J]);return $(E,he,void 0,void 0,null,W)},ye.createRef=function(){return{current:null}},ye.forwardRef=function(E){return{$$typeof:v,render:E}},ye.isValidElement=oe,ye.lazy=function(E){return{$$typeof:x,_payload:{_status:-1,_result:E},_init:Q}},ye.memo=function(E,V){return{$$typeof:g,type:E,compare:V===void 0?null:V}},ye.startTransition=function(E){var V=G.T,F={};G.T=F;try{var J=E(),W=G.S;W!==null&&W(F,J),typeof J=="object"&&J!==null&&typeof J.then=="function"&&J.then(ae,B)}catch(he){B(he)}finally{G.T=V}},ye.unstable_useCacheRefresh=function(){return G.H.useCacheRefresh()},ye.use=function(E){return G.H.use(E)},ye.useActionState=function(E,V,F){return G.H.useActionState(E,V,F)},ye.useCallback=function(E,V){return G.H.useCallback(E,V)},ye.useContext=function(E){return G.H.useContext(E)},ye.useDebugValue=function(){},ye.useDeferredValue=function(E,V){return G.H.useDeferredValue(E,V)},ye.useEffect=function(E,V,F){var J=G.H;if(typeof F=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return J.useEffect(E,V)},ye.useId=function(){return G.H.useId()},ye.useImperativeHandle=function(E,V,F){return G.H.useImperativeHandle(E,V,F)},ye.useInsertionEffect=function(E,V){return G.H.useInsertionEffect(E,V)},ye.useLayoutEffect=function(E,V){return G.H.useLayoutEffect(E,V)},ye.useMemo=function(E,V){return G.H.useMemo(E,V)},ye.useOptimistic=function(E,V){return G.H.useOptimistic(E,V)},ye.useReducer=function(E,V,F){return G.H.useReducer(E,V,F)},ye.useRef=function(E){return G.H.useRef(E)},ye.useState=function(E){return G.H.useState(E)},ye.useSyncExternalStore=function(E,V,F){return G.H.useSyncExternalStore(E,V,F)},ye.useTransition=function(){return G.H.useTransition()},ye.version="19.1.0",ye}var Zh;function Ts(){return Zh||(Zh=1,Xu.exports=Hb()),Xu.exports}var b=Ts();const Wn=Uv(b),Hv=jb({__proto__:null,default:Wn},[b]);var Qu={exports:{}},_i={},Zu={exports:{}},Ku={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kh;function Bb(){return Kh||(Kh=1,function(a){function o(M,Q){var B=M.length;M.push(Q);e:for(;0<B;){var ae=B-1>>>1,E=M[ae];if(0<s(E,Q))M[ae]=Q,M[B]=E,B=ae;else break e}}function u(M){return M.length===0?null:M[0]}function c(M){if(M.length===0)return null;var Q=M[0],B=M.pop();if(B!==Q){M[0]=B;e:for(var ae=0,E=M.length,V=E>>>1;ae<V;){var F=2*(ae+1)-1,J=M[F],W=F+1,he=M[W];if(0>s(J,B))W<E&&0>s(he,J)?(M[ae]=he,M[W]=B,ae=W):(M[ae]=J,M[F]=B,ae=F);else if(W<E&&0>s(he,B))M[ae]=he,M[W]=B,ae=W;else break e}}return Q}function s(M,Q){var B=M.sortIndex-Q.sortIndex;return B!==0?B:M.id-Q.id}if(a.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var d=performance;a.unstable_now=function(){return d.now()}}else{var h=Date,v=h.now();a.unstable_now=function(){return h.now()-v}}var y=[],g=[],x=1,w=null,R=3,N=!1,z=!1,T=!1,D=!1,L=typeof setTimeout=="function"?setTimeout:null,k=typeof clearTimeout=="function"?clearTimeout:null,_=typeof setImmediate<"u"?setImmediate:null;function Z(M){for(var Q=u(g);Q!==null;){if(Q.callback===null)c(g);else if(Q.startTime<=M)c(g),Q.sortIndex=Q.expirationTime,o(y,Q);else break;Q=u(g)}}function G(M){if(T=!1,Z(M),!z)if(u(y)!==null)z=!0,I||(I=!0,se());else{var Q=u(g);Q!==null&&fe(G,Q.startTime-M)}}var I=!1,$=-1,K=5,oe=-1;function ve(){return D?!0:!(a.unstable_now()-oe<K)}function pe(){if(D=!1,I){var M=a.unstable_now();oe=M;var Q=!0;try{e:{z=!1,T&&(T=!1,k($),$=-1),N=!0;var B=R;try{t:{for(Z(M),w=u(y);w!==null&&!(w.expirationTime>M&&ve());){var ae=w.callback;if(typeof ae=="function"){w.callback=null,R=w.priorityLevel;var E=ae(w.expirationTime<=M);if(M=a.unstable_now(),typeof E=="function"){w.callback=E,Z(M),Q=!0;break t}w===u(y)&&c(y),Z(M)}else c(y);w=u(y)}if(w!==null)Q=!0;else{var V=u(g);V!==null&&fe(G,V.startTime-M),Q=!1}}break e}finally{w=null,R=B,N=!1}Q=void 0}}finally{Q?se():I=!1}}}var se;if(typeof _=="function")se=function(){_(pe)};else if(typeof MessageChannel<"u"){var ge=new MessageChannel,me=ge.port2;ge.port1.onmessage=pe,se=function(){me.postMessage(null)}}else se=function(){L(pe,0)};function fe(M,Q){$=L(function(){M(a.unstable_now())},Q)}a.unstable_IdlePriority=5,a.unstable_ImmediatePriority=1,a.unstable_LowPriority=4,a.unstable_NormalPriority=3,a.unstable_Profiling=null,a.unstable_UserBlockingPriority=2,a.unstable_cancelCallback=function(M){M.callback=null},a.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<M?Math.floor(1e3/M):5},a.unstable_getCurrentPriorityLevel=function(){return R},a.unstable_next=function(M){switch(R){case 1:case 2:case 3:var Q=3;break;default:Q=R}var B=R;R=Q;try{return M()}finally{R=B}},a.unstable_requestPaint=function(){D=!0},a.unstable_runWithPriority=function(M,Q){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var B=R;R=M;try{return Q()}finally{R=B}},a.unstable_scheduleCallback=function(M,Q,B){var ae=a.unstable_now();switch(typeof B=="object"&&B!==null?(B=B.delay,B=typeof B=="number"&&0<B?ae+B:ae):B=ae,M){case 1:var E=-1;break;case 2:E=250;break;case 5:E=1073741823;break;case 4:E=1e4;break;default:E=5e3}return E=B+E,M={id:x++,callback:Q,priorityLevel:M,startTime:B,expirationTime:E,sortIndex:-1},B>ae?(M.sortIndex=B,o(g,M),u(y)===null&&M===u(g)&&(T?(k($),$=-1):T=!0,fe(G,B-ae))):(M.sortIndex=E,o(y,M),z||N||(z=!0,I||(I=!0,se()))),M},a.unstable_shouldYield=ve,a.unstable_wrapCallback=function(M){var Q=R;return function(){var B=R;R=Q;try{return M.apply(this,arguments)}finally{R=B}}}}(Ku)),Ku}var Jh;function Lb(){return Jh||(Jh=1,Zu.exports=Bb()),Zu.exports}var Ju={exports:{}},st={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $h;function kb(){if($h)return st;$h=1;var a=Ts();function o(y){var g="https://react.dev/errors/"+y;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var x=2;x<arguments.length;x++)g+="&args[]="+encodeURIComponent(arguments[x])}return"Minified React error #"+y+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function u(){}var c={d:{f:u,r:function(){throw Error(o(522))},D:u,C:u,L:u,m:u,X:u,S:u,M:u},p:0,findDOMNode:null},s=Symbol.for("react.portal");function d(y,g,x){var w=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:s,key:w==null?null:""+w,children:y,containerInfo:g,implementation:x}}var h=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function v(y,g){if(y==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return st.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,st.createPortal=function(y,g){var x=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(o(299));return d(y,g,null,x)},st.flushSync=function(y){var g=h.T,x=c.p;try{if(h.T=null,c.p=2,y)return y()}finally{h.T=g,c.p=x,c.d.f()}},st.preconnect=function(y,g){typeof y=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,c.d.C(y,g))},st.prefetchDNS=function(y){typeof y=="string"&&c.d.D(y)},st.preinit=function(y,g){if(typeof y=="string"&&g&&typeof g.as=="string"){var x=g.as,w=v(x,g.crossOrigin),R=typeof g.integrity=="string"?g.integrity:void 0,N=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;x==="style"?c.d.S(y,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:w,integrity:R,fetchPriority:N}):x==="script"&&c.d.X(y,{crossOrigin:w,integrity:R,fetchPriority:N,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},st.preinitModule=function(y,g){if(typeof y=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var x=v(g.as,g.crossOrigin);c.d.M(y,{crossOrigin:x,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&c.d.M(y)},st.preload=function(y,g){if(typeof y=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var x=g.as,w=v(x,g.crossOrigin);c.d.L(y,x,{crossOrigin:w,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},st.preloadModule=function(y,g){if(typeof y=="string")if(g){var x=v(g.as,g.crossOrigin);c.d.m(y,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:x,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else c.d.m(y)},st.requestFormReset=function(y){c.d.r(y)},st.unstable_batchedUpdates=function(y,g){return y(g)},st.useFormState=function(y,g,x){return h.H.useFormState(y,g,x)},st.useFormStatus=function(){return h.H.useHostTransitionStatus()},st.version="19.1.0",st}var Ph;function Bv(){if(Ph)return Ju.exports;Ph=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(o){console.error(o)}}return a(),Ju.exports=kb(),Ju.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Fh;function qb(){if(Fh)return _i;Fh=1;var a=Lb(),o=Ts(),u=Bv();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function d(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function h(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function v(e){if(d(e)!==e)throw Error(c(188))}function y(e){var t=e.alternate;if(!t){if(t=d(e),t===null)throw Error(c(188));return t!==e?null:e}for(var n=e,l=t;;){var i=n.return;if(i===null)break;var r=i.alternate;if(r===null){if(l=i.return,l!==null){n=l;continue}break}if(i.child===r.child){for(r=i.child;r;){if(r===n)return v(i),e;if(r===l)return v(i),t;r=r.sibling}throw Error(c(188))}if(n.return!==l.return)n=i,l=r;else{for(var f=!1,p=i.child;p;){if(p===n){f=!0,n=i,l=r;break}if(p===l){f=!0,l=i,n=r;break}p=p.sibling}if(!f){for(p=r.child;p;){if(p===n){f=!0,n=r,l=i;break}if(p===l){f=!0,l=r,n=i;break}p=p.sibling}if(!f)throw Error(c(189))}}if(n.alternate!==l)throw Error(c(190))}if(n.tag!==3)throw Error(c(188));return n.stateNode.current===n?e:t}function g(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=g(e),t!==null)return t;e=e.sibling}return null}var x=Object.assign,w=Symbol.for("react.element"),R=Symbol.for("react.transitional.element"),N=Symbol.for("react.portal"),z=Symbol.for("react.fragment"),T=Symbol.for("react.strict_mode"),D=Symbol.for("react.profiler"),L=Symbol.for("react.provider"),k=Symbol.for("react.consumer"),_=Symbol.for("react.context"),Z=Symbol.for("react.forward_ref"),G=Symbol.for("react.suspense"),I=Symbol.for("react.suspense_list"),$=Symbol.for("react.memo"),K=Symbol.for("react.lazy"),oe=Symbol.for("react.activity"),ve=Symbol.for("react.memo_cache_sentinel"),pe=Symbol.iterator;function se(e){return e===null||typeof e!="object"?null:(e=pe&&e[pe]||e["@@iterator"],typeof e=="function"?e:null)}var ge=Symbol.for("react.client.reference");function me(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===ge?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case z:return"Fragment";case D:return"Profiler";case T:return"StrictMode";case G:return"Suspense";case I:return"SuspenseList";case oe:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case N:return"Portal";case _:return(e.displayName||"Context")+".Provider";case k:return(e._context.displayName||"Context")+".Consumer";case Z:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case $:return t=e.displayName||null,t!==null?t:me(e.type)||"Memo";case K:t=e._payload,e=e._init;try{return me(e(t))}catch{}}return null}var fe=Array.isArray,M=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q=u.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,B={pending:!1,data:null,method:null,action:null},ae=[],E=-1;function V(e){return{current:e}}function F(e){0>E||(e.current=ae[E],ae[E]=null,E--)}function J(e,t){E++,ae[E]=e.current,e.current=t}var W=V(null),he=V(null),ie=V(null),P=V(null);function ce(e,t){switch(J(ie,t),J(he,e),J(W,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?gh(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=gh(t),e=yh(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}F(W),J(W,e)}function _e(){F(W),F(he),F(ie)}function Ae(e){e.memoizedState!==null&&J(P,e);var t=W.current,n=yh(t,e.type);t!==n&&(J(he,e),J(W,n))}function Ce(e){he.current===e&&(F(W),F(he)),P.current===e&&(F(P),Ni._currentValue=B)}var je=Object.prototype.hasOwnProperty,rt=a.unstable_scheduleCallback,qt=a.unstable_cancelCallback,_a=a.unstable_shouldYield,ja=a.unstable_requestPaint,mt=a.unstable_now,my=a.unstable_getCurrentPriorityLevel,Ps=a.unstable_ImmediatePriority,Fs=a.unstable_UserBlockingPriority,Vi=a.unstable_NormalPriority,hy=a.unstable_LowPriority,Ws=a.unstable_IdlePriority,vy=a.log,py=a.unstable_setDisableYieldValue,za=null,bt=null;function Rn(e){if(typeof vy=="function"&&py(e),bt&&typeof bt.setStrictMode=="function")try{bt.setStrictMode(za,e)}catch{}}var xt=Math.clz32?Math.clz32:by,gy=Math.log,yy=Math.LN2;function by(e){return e>>>=0,e===0?32:31-(gy(e)/yy|0)|0}var Xi=256,Qi=4194304;function ol(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Zi(e,t,n){var l=e.pendingLanes;if(l===0)return 0;var i=0,r=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var p=l&134217727;return p!==0?(l=p&~r,l!==0?i=ol(l):(f&=p,f!==0?i=ol(f):n||(n=p&~e,n!==0&&(i=ol(n))))):(p=l&~r,p!==0?i=ol(p):f!==0?i=ol(f):n||(n=l&~e,n!==0&&(i=ol(n)))),i===0?0:t!==0&&t!==i&&(t&r)===0&&(r=i&-i,n=t&-t,r>=n||r===32&&(n&4194048)!==0)?t:i}function Ua(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function xy(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Is(){var e=Xi;return Xi<<=1,(Xi&4194048)===0&&(Xi=256),e}function ef(){var e=Qi;return Qi<<=1,(Qi&62914560)===0&&(Qi=4194304),e}function _r(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ha(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Sy(e,t,n,l,i,r){var f=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var p=e.entanglements,S=e.expirationTimes,j=e.hiddenUpdates;for(n=f&~n;0<n;){var q=31-xt(n),X=1<<q;p[q]=0,S[q]=-1;var U=j[q];if(U!==null)for(j[q]=null,q=0;q<U.length;q++){var H=U[q];H!==null&&(H.lane&=-536870913)}n&=~X}l!==0&&tf(e,l,0),r!==0&&i===0&&e.tag!==0&&(e.suspendedLanes|=r&~(f&~t))}function tf(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-xt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|n&4194090}function nf(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var l=31-xt(n),i=1<<l;i&t|e[l]&t&&(e[l]|=t),n&=~i}}function jr(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function zr(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function lf(){var e=Q.p;return e!==0?e:(e=window.event,e===void 0?32:Bh(e.type))}function wy(e,t){var n=Q.p;try{return Q.p=e,t()}finally{Q.p=n}}var Cn=Math.random().toString(36).slice(2),ct="__reactFiber$"+Cn,ht="__reactProps$"+Cn,Ol="__reactContainer$"+Cn,Ur="__reactEvents$"+Cn,Ey="__reactListeners$"+Cn,Ty="__reactHandles$"+Cn,af="__reactResources$"+Cn,Ba="__reactMarker$"+Cn;function Hr(e){delete e[ct],delete e[ht],delete e[Ur],delete e[Ey],delete e[Ty]}function _l(e){var t=e[ct];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ol]||n[ct]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=wh(e);e!==null;){if(n=e[ct])return n;e=wh(e)}return t}e=n,n=e.parentNode}return null}function jl(e){if(e=e[ct]||e[Ol]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function La(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function zl(e){var t=e[af];return t||(t=e[af]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function et(e){e[Ba]=!0}var of=new Set,rf={};function rl(e,t){Ul(e,t),Ul(e+"Capture",t)}function Ul(e,t){for(rf[e]=t,e=0;e<t.length;e++)of.add(t[e])}var Ay=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),cf={},uf={};function Ny(e){return je.call(uf,e)?!0:je.call(cf,e)?!1:Ay.test(e)?uf[e]=!0:(cf[e]=!0,!1)}function Ki(e,t,n){if(Ny(t))if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+n)}}function Ji(e,t,n){if(n===null)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+n)}}function un(e,t,n,l){if(l===null)e.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttributeNS(t,n,""+l)}}var Br,sf;function Hl(e){if(Br===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Br=t&&t[1]||"",sf=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Br+e+sf}var Lr=!1;function kr(e,t){if(!e||Lr)return"";Lr=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var X=function(){throw Error()};if(Object.defineProperty(X.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(X,[])}catch(H){var U=H}Reflect.construct(e,[],X)}else{try{X.call()}catch(H){U=H}e.call(X.prototype)}}else{try{throw Error()}catch(H){U=H}(X=e())&&typeof X.catch=="function"&&X.catch(function(){})}}catch(H){if(H&&U&&typeof H.stack=="string")return[H.stack,U.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var i=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");i&&i.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var r=l.DetermineComponentFrameRoot(),f=r[0],p=r[1];if(f&&p){var S=f.split(`
`),j=p.split(`
`);for(i=l=0;l<S.length&&!S[l].includes("DetermineComponentFrameRoot");)l++;for(;i<j.length&&!j[i].includes("DetermineComponentFrameRoot");)i++;if(l===S.length||i===j.length)for(l=S.length-1,i=j.length-1;1<=l&&0<=i&&S[l]!==j[i];)i--;for(;1<=l&&0<=i;l--,i--)if(S[l]!==j[i]){if(l!==1||i!==1)do if(l--,i--,0>i||S[l]!==j[i]){var q=`
`+S[l].replace(" at new "," at ");return e.displayName&&q.includes("<anonymous>")&&(q=q.replace("<anonymous>",e.displayName)),q}while(1<=l&&0<=i);break}}}finally{Lr=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?Hl(n):""}function Ry(e){switch(e.tag){case 26:case 27:case 5:return Hl(e.type);case 16:return Hl("Lazy");case 13:return Hl("Suspense");case 19:return Hl("SuspenseList");case 0:case 15:return kr(e.type,!1);case 11:return kr(e.type.render,!1);case 1:return kr(e.type,!0);case 31:return Hl("Activity");default:return""}}function ff(e){try{var t="";do t+=Ry(e),e=e.return;while(e);return t}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function Mt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function df(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Cy(e){var t=df(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,r=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(f){l=""+f,r.call(this,f)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function $i(e){e._valueTracker||(e._valueTracker=Cy(e))}function mf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),l="";return e&&(l=df(e)?e.checked?"true":"false":e.value),e=l,e!==n?(t.setValue(e),!0):!1}function Pi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Dy=/[\n"\\]/g;function Ot(e){return e.replace(Dy,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function qr(e,t,n,l,i,r,f,p){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+Mt(t)):e.value!==""+Mt(t)&&(e.value=""+Mt(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?Gr(e,f,Mt(t)):n!=null?Gr(e,f,Mt(n)):l!=null&&e.removeAttribute("value"),i==null&&r!=null&&(e.defaultChecked=!!r),i!=null&&(e.checked=i&&typeof i!="function"&&typeof i!="symbol"),p!=null&&typeof p!="function"&&typeof p!="symbol"&&typeof p!="boolean"?e.name=""+Mt(p):e.removeAttribute("name")}function hf(e,t,n,l,i,r,f,p){if(r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.type=r),t!=null||n!=null){if(!(r!=="submit"&&r!=="reset"||t!=null))return;n=n!=null?""+Mt(n):"",t=t!=null?""+Mt(t):n,p||t===e.value||(e.value=t),e.defaultValue=t}l=l??i,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=p?e.checked:!!l,e.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function Gr(e,t,n){t==="number"&&Pi(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function Bl(e,t,n,l){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&l&&(e[n].defaultSelected=!0)}else{for(n=""+Mt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,l&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function vf(e,t,n){if(t!=null&&(t=""+Mt(t),t!==e.value&&(e.value=t),n==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=n!=null?""+Mt(n):""}function pf(e,t,n,l){if(t==null){if(l!=null){if(n!=null)throw Error(c(92));if(fe(l)){if(1<l.length)throw Error(c(93));l=l[0]}n=l}n==null&&(n=""),t=n}n=Mt(t),e.defaultValue=n,l=e.textContent,l===n&&l!==""&&l!==null&&(e.value=l)}function Ll(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var My=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function gf(e,t,n){var l=t.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,n):typeof n!="number"||n===0||My.has(t)?t==="float"?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function yf(e,t,n){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var i in t)l=t[i],t.hasOwnProperty(i)&&n[i]!==l&&gf(e,i,l)}else for(var r in t)t.hasOwnProperty(r)&&gf(e,r,t[r])}function Yr(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Oy=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),_y=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Fi(e){return _y.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Vr=null;function Xr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var kl=null,ql=null;function bf(e){var t=jl(e);if(t&&(e=t.stateNode)){var n=e[ht]||null;e:switch(e=t.stateNode,t.type){case"input":if(qr(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+Ot(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var l=n[t];if(l!==e&&l.form===e.form){var i=l[ht]||null;if(!i)throw Error(c(90));qr(l,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name)}}for(t=0;t<n.length;t++)l=n[t],l.form===e.form&&mf(l)}break e;case"textarea":vf(e,n.value,n.defaultValue);break e;case"select":t=n.value,t!=null&&Bl(e,!!n.multiple,t,!1)}}}var Qr=!1;function xf(e,t,n){if(Qr)return e(t,n);Qr=!0;try{var l=e(t);return l}finally{if(Qr=!1,(kl!==null||ql!==null)&&(Bo(),kl&&(t=kl,e=ql,ql=kl=null,bf(t),e)))for(t=0;t<e.length;t++)bf(e[t])}}function ka(e,t){var n=e.stateNode;if(n===null)return null;var l=n[ht]||null;if(l===null)return null;n=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(c(231,t,typeof n));return n}var sn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Zr=!1;if(sn)try{var qa={};Object.defineProperty(qa,"passive",{get:function(){Zr=!0}}),window.addEventListener("test",qa,qa),window.removeEventListener("test",qa,qa)}catch{Zr=!1}var Dn=null,Kr=null,Wi=null;function Sf(){if(Wi)return Wi;var e,t=Kr,n=t.length,l,i="value"in Dn?Dn.value:Dn.textContent,r=i.length;for(e=0;e<n&&t[e]===i[e];e++);var f=n-e;for(l=1;l<=f&&t[n-l]===i[r-l];l++);return Wi=i.slice(e,1<l?1-l:void 0)}function Ii(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function eo(){return!0}function wf(){return!1}function vt(e){function t(n,l,i,r,f){this._reactName=n,this._targetInst=i,this.type=l,this.nativeEvent=r,this.target=f,this.currentTarget=null;for(var p in e)e.hasOwnProperty(p)&&(n=e[p],this[p]=n?n(r):r[p]);return this.isDefaultPrevented=(r.defaultPrevented!=null?r.defaultPrevented:r.returnValue===!1)?eo:wf,this.isPropagationStopped=wf,this}return x(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=eo)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=eo)},persist:function(){},isPersistent:eo}),t}var cl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},to=vt(cl),Ga=x({},cl,{view:0,detail:0}),jy=vt(Ga),Jr,$r,Ya,no=x({},Ga,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Fr,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ya&&(Ya&&e.type==="mousemove"?(Jr=e.screenX-Ya.screenX,$r=e.screenY-Ya.screenY):$r=Jr=0,Ya=e),Jr)},movementY:function(e){return"movementY"in e?e.movementY:$r}}),Ef=vt(no),zy=x({},no,{dataTransfer:0}),Uy=vt(zy),Hy=x({},Ga,{relatedTarget:0}),Pr=vt(Hy),By=x({},cl,{animationName:0,elapsedTime:0,pseudoElement:0}),Ly=vt(By),ky=x({},cl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),qy=vt(ky),Gy=x({},cl,{data:0}),Tf=vt(Gy),Yy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Vy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Xy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Qy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Xy[e])?!!t[e]:!1}function Fr(){return Qy}var Zy=x({},Ga,{key:function(e){if(e.key){var t=Yy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ii(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Vy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Fr,charCode:function(e){return e.type==="keypress"?Ii(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ii(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Ky=vt(Zy),Jy=x({},no,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Af=vt(Jy),$y=x({},Ga,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Fr}),Py=vt($y),Fy=x({},cl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Wy=vt(Fy),Iy=x({},no,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),e0=vt(Iy),t0=x({},cl,{newState:0,oldState:0}),n0=vt(t0),l0=[9,13,27,32],Wr=sn&&"CompositionEvent"in window,Va=null;sn&&"documentMode"in document&&(Va=document.documentMode);var a0=sn&&"TextEvent"in window&&!Va,Nf=sn&&(!Wr||Va&&8<Va&&11>=Va),Rf=" ",Cf=!1;function Df(e,t){switch(e){case"keyup":return l0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Mf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Gl=!1;function i0(e,t){switch(e){case"compositionend":return Mf(t);case"keypress":return t.which!==32?null:(Cf=!0,Rf);case"textInput":return e=t.data,e===Rf&&Cf?null:e;default:return null}}function o0(e,t){if(Gl)return e==="compositionend"||!Wr&&Df(e,t)?(e=Sf(),Wi=Kr=Dn=null,Gl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nf&&t.locale!=="ko"?null:t.data;default:return null}}var r0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Of(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!r0[e.type]:t==="textarea"}function _f(e,t,n,l){kl?ql?ql.push(l):ql=[l]:kl=l,t=Vo(t,"onChange"),0<t.length&&(n=new to("onChange","change",null,n,l),e.push({event:n,listeners:t}))}var Xa=null,Qa=null;function c0(e){dh(e,0)}function lo(e){var t=La(e);if(mf(t))return e}function jf(e,t){if(e==="change")return t}var zf=!1;if(sn){var Ir;if(sn){var ec="oninput"in document;if(!ec){var Uf=document.createElement("div");Uf.setAttribute("oninput","return;"),ec=typeof Uf.oninput=="function"}Ir=ec}else Ir=!1;zf=Ir&&(!document.documentMode||9<document.documentMode)}function Hf(){Xa&&(Xa.detachEvent("onpropertychange",Bf),Qa=Xa=null)}function Bf(e){if(e.propertyName==="value"&&lo(Qa)){var t=[];_f(t,Qa,e,Xr(e)),xf(c0,t)}}function u0(e,t,n){e==="focusin"?(Hf(),Xa=t,Qa=n,Xa.attachEvent("onpropertychange",Bf)):e==="focusout"&&Hf()}function s0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return lo(Qa)}function f0(e,t){if(e==="click")return lo(t)}function d0(e,t){if(e==="input"||e==="change")return lo(t)}function m0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var St=typeof Object.is=="function"?Object.is:m0;function Za(e,t){if(St(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var i=n[l];if(!je.call(t,i)||!St(e[i],t[i]))return!1}return!0}function Lf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function kf(e,t){var n=Lf(e);e=0;for(var l;n;){if(n.nodeType===3){if(l=e+n.textContent.length,e<=t&&l>=t)return{node:n,offset:t-e};e=l}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Lf(n)}}function qf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?qf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Gf(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Pi(e.document);t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Pi(e.document)}return t}function tc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var h0=sn&&"documentMode"in document&&11>=document.documentMode,Yl=null,nc=null,Ka=null,lc=!1;function Yf(e,t,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;lc||Yl==null||Yl!==Pi(l)||(l=Yl,"selectionStart"in l&&tc(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Ka&&Za(Ka,l)||(Ka=l,l=Vo(nc,"onSelect"),0<l.length&&(t=new to("onSelect","select",null,t,n),e.push({event:t,listeners:l}),t.target=Yl)))}function ul(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Vl={animationend:ul("Animation","AnimationEnd"),animationiteration:ul("Animation","AnimationIteration"),animationstart:ul("Animation","AnimationStart"),transitionrun:ul("Transition","TransitionRun"),transitionstart:ul("Transition","TransitionStart"),transitioncancel:ul("Transition","TransitionCancel"),transitionend:ul("Transition","TransitionEnd")},ac={},Vf={};sn&&(Vf=document.createElement("div").style,"AnimationEvent"in window||(delete Vl.animationend.animation,delete Vl.animationiteration.animation,delete Vl.animationstart.animation),"TransitionEvent"in window||delete Vl.transitionend.transition);function sl(e){if(ac[e])return ac[e];if(!Vl[e])return e;var t=Vl[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Vf)return ac[e]=t[n];return e}var Xf=sl("animationend"),Qf=sl("animationiteration"),Zf=sl("animationstart"),v0=sl("transitionrun"),p0=sl("transitionstart"),g0=sl("transitioncancel"),Kf=sl("transitionend"),Jf=new Map,ic="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");ic.push("scrollEnd");function Gt(e,t){Jf.set(e,t),rl(t,[e])}var $f=new WeakMap;function _t(e,t){if(typeof e=="object"&&e!==null){var n=$f.get(e);return n!==void 0?n:(t={value:e,source:t,stack:ff(t)},$f.set(e,t),t)}return{value:e,source:t,stack:ff(t)}}var jt=[],Xl=0,oc=0;function ao(){for(var e=Xl,t=oc=Xl=0;t<e;){var n=jt[t];jt[t++]=null;var l=jt[t];jt[t++]=null;var i=jt[t];jt[t++]=null;var r=jt[t];if(jt[t++]=null,l!==null&&i!==null){var f=l.pending;f===null?i.next=i:(i.next=f.next,f.next=i),l.pending=i}r!==0&&Pf(n,i,r)}}function io(e,t,n,l){jt[Xl++]=e,jt[Xl++]=t,jt[Xl++]=n,jt[Xl++]=l,oc|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function rc(e,t,n,l){return io(e,t,n,l),oo(e)}function Ql(e,t){return io(e,null,null,t),oo(e)}function Pf(e,t,n){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n);for(var i=!1,r=e.return;r!==null;)r.childLanes|=n,l=r.alternate,l!==null&&(l.childLanes|=n),r.tag===22&&(e=r.stateNode,e===null||e._visibility&1||(i=!0)),e=r,r=r.return;return e.tag===3?(r=e.stateNode,i&&t!==null&&(i=31-xt(n),e=r.hiddenUpdates,l=e[i],l===null?e[i]=[t]:l.push(t),t.lane=n|536870912),r):null}function oo(e){if(50<yi)throw yi=0,mu=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Zl={};function y0(e,t,n,l){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function wt(e,t,n,l){return new y0(e,t,n,l)}function cc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function fn(e,t){var n=e.alternate;return n===null?(n=wt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&65011712,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ff(e,t){e.flags&=65011714;var n=e.alternate;return n===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function ro(e,t,n,l,i,r){var f=0;if(l=e,typeof e=="function")cc(e)&&(f=1);else if(typeof e=="string")f=xb(e,n,W.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case oe:return e=wt(31,n,t,i),e.elementType=oe,e.lanes=r,e;case z:return fl(n.children,i,r,t);case T:f=8,i|=24;break;case D:return e=wt(12,n,t,i|2),e.elementType=D,e.lanes=r,e;case G:return e=wt(13,n,t,i),e.elementType=G,e.lanes=r,e;case I:return e=wt(19,n,t,i),e.elementType=I,e.lanes=r,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case L:case _:f=10;break e;case k:f=9;break e;case Z:f=11;break e;case $:f=14;break e;case K:f=16,l=null;break e}f=29,n=Error(c(130,e===null?"null":typeof e,"")),l=null}return t=wt(f,n,t,i),t.elementType=e,t.type=l,t.lanes=r,t}function fl(e,t,n,l){return e=wt(7,e,l,t),e.lanes=n,e}function uc(e,t,n){return e=wt(6,e,null,t),e.lanes=n,e}function sc(e,t,n){return t=wt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Kl=[],Jl=0,co=null,uo=0,zt=[],Ut=0,dl=null,dn=1,mn="";function ml(e,t){Kl[Jl++]=uo,Kl[Jl++]=co,co=e,uo=t}function Wf(e,t,n){zt[Ut++]=dn,zt[Ut++]=mn,zt[Ut++]=dl,dl=e;var l=dn;e=mn;var i=32-xt(l)-1;l&=~(1<<i),n+=1;var r=32-xt(t)+i;if(30<r){var f=i-i%5;r=(l&(1<<f)-1).toString(32),l>>=f,i-=f,dn=1<<32-xt(t)+i|n<<i|l,mn=r+e}else dn=1<<r|n<<i|l,mn=e}function fc(e){e.return!==null&&(ml(e,1),Wf(e,1,0))}function dc(e){for(;e===co;)co=Kl[--Jl],Kl[Jl]=null,uo=Kl[--Jl],Kl[Jl]=null;for(;e===dl;)dl=zt[--Ut],zt[Ut]=null,mn=zt[--Ut],zt[Ut]=null,dn=zt[--Ut],zt[Ut]=null}var dt=null,Xe=null,Oe=!1,hl=null,Wt=!1,mc=Error(c(519));function vl(e){var t=Error(c(418,""));throw Pa(_t(t,e)),mc}function If(e){var t=e.stateNode,n=e.type,l=e.memoizedProps;switch(t[ct]=e,t[ht]=l,n){case"dialog":Ee("cancel",t),Ee("close",t);break;case"iframe":case"object":case"embed":Ee("load",t);break;case"video":case"audio":for(n=0;n<xi.length;n++)Ee(xi[n],t);break;case"source":Ee("error",t);break;case"img":case"image":case"link":Ee("error",t),Ee("load",t);break;case"details":Ee("toggle",t);break;case"input":Ee("invalid",t),hf(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),$i(t);break;case"select":Ee("invalid",t);break;case"textarea":Ee("invalid",t),pf(t,l.value,l.defaultValue,l.children),$i(t)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||t.textContent===""+n||l.suppressHydrationWarning===!0||ph(t.textContent,n)?(l.popover!=null&&(Ee("beforetoggle",t),Ee("toggle",t)),l.onScroll!=null&&Ee("scroll",t),l.onScrollEnd!=null&&Ee("scrollend",t),l.onClick!=null&&(t.onclick=Xo),t=!0):t=!1,t||vl(e)}function ed(e){for(dt=e.return;dt;)switch(dt.tag){case 5:case 13:Wt=!1;return;case 27:case 3:Wt=!0;return;default:dt=dt.return}}function Ja(e){if(e!==dt)return!1;if(!Oe)return ed(e),Oe=!0,!1;var t=e.tag,n;if((n=t!==3&&t!==27)&&((n=t===5)&&(n=e.type,n=!(n!=="form"&&n!=="button")||Du(e.type,e.memoizedProps)),n=!n),n&&Xe&&vl(e),ed(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(n=e.data,n==="/$"){if(t===0){Xe=Vt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++;e=e.nextSibling}Xe=null}}else t===27?(t=Xe,Qn(e.type)?(e=ju,ju=null,Xe=e):Xe=t):Xe=dt?Vt(e.stateNode.nextSibling):null;return!0}function $a(){Xe=dt=null,Oe=!1}function td(){var e=hl;return e!==null&&(yt===null?yt=e:yt.push.apply(yt,e),hl=null),e}function Pa(e){hl===null?hl=[e]:hl.push(e)}var hc=V(null),pl=null,hn=null;function Mn(e,t,n){J(hc,t._currentValue),t._currentValue=n}function vn(e){e._currentValue=hc.current,F(hc)}function vc(e,t,n){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===n)break;e=e.return}}function pc(e,t,n,l){var i=e.child;for(i!==null&&(i.return=e);i!==null;){var r=i.dependencies;if(r!==null){var f=i.child;r=r.firstContext;e:for(;r!==null;){var p=r;r=i;for(var S=0;S<t.length;S++)if(p.context===t[S]){r.lanes|=n,p=r.alternate,p!==null&&(p.lanes|=n),vc(r.return,n,e),l||(f=null);break e}r=p.next}}else if(i.tag===18){if(f=i.return,f===null)throw Error(c(341));f.lanes|=n,r=f.alternate,r!==null&&(r.lanes|=n),vc(f,n,e),f=null}else f=i.child;if(f!==null)f.return=i;else for(f=i;f!==null;){if(f===e){f=null;break}if(i=f.sibling,i!==null){i.return=f.return,f=i;break}f=f.return}i=f}}function Fa(e,t,n,l){e=null;for(var i=t,r=!1;i!==null;){if(!r){if((i.flags&524288)!==0)r=!0;else if((i.flags&262144)!==0)break}if(i.tag===10){var f=i.alternate;if(f===null)throw Error(c(387));if(f=f.memoizedProps,f!==null){var p=i.type;St(i.pendingProps.value,f.value)||(e!==null?e.push(p):e=[p])}}else if(i===P.current){if(f=i.alternate,f===null)throw Error(c(387));f.memoizedState.memoizedState!==i.memoizedState.memoizedState&&(e!==null?e.push(Ni):e=[Ni])}i=i.return}e!==null&&pc(t,e,n,l),t.flags|=262144}function so(e){for(e=e.firstContext;e!==null;){if(!St(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function gl(e){pl=e,hn=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ut(e){return nd(pl,e)}function fo(e,t){return pl===null&&gl(e),nd(e,t)}function nd(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},hn===null){if(e===null)throw Error(c(308));hn=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else hn=hn.next=t;return n}var b0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(n,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(n){return n()})}},x0=a.unstable_scheduleCallback,S0=a.unstable_NormalPriority,Fe={$$typeof:_,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function gc(){return{controller:new b0,data:new Map,refCount:0}}function Wa(e){e.refCount--,e.refCount===0&&x0(S0,function(){e.controller.abort()})}var Ia=null,yc=0,$l=0,Pl=null;function w0(e,t){if(Ia===null){var n=Ia=[];yc=0,$l=xu(),Pl={status:"pending",value:void 0,then:function(l){n.push(l)}}}return yc++,t.then(ld,ld),t}function ld(){if(--yc===0&&Ia!==null){Pl!==null&&(Pl.status="fulfilled");var e=Ia;Ia=null,$l=0,Pl=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function E0(e,t){var n=[],l={status:"pending",value:null,reason:null,then:function(i){n.push(i)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var i=0;i<n.length;i++)(0,n[i])(t)},function(i){for(l.status="rejected",l.reason=i,i=0;i<n.length;i++)(0,n[i])(void 0)}),l}var ad=M.S;M.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&w0(e,t),ad!==null&&ad(e,t)};var yl=V(null);function bc(){var e=yl.current;return e!==null?e:qe.pooledCache}function mo(e,t){t===null?J(yl,yl.current):J(yl,t.pool)}function id(){var e=bc();return e===null?null:{parent:Fe._currentValue,pool:e}}var ei=Error(c(460)),od=Error(c(474)),ho=Error(c(542)),xc={then:function(){}};function rd(e){return e=e.status,e==="fulfilled"||e==="rejected"}function vo(){}function cd(e,t,n){switch(n=e[n],n===void 0?e.push(t):n!==t&&(t.then(vo,vo),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,sd(e),e;default:if(typeof t.status=="string")t.then(vo,vo);else{if(e=qe,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var i=t;i.status="fulfilled",i.value=l}},function(l){if(t.status==="pending"){var i=t;i.status="rejected",i.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,sd(e),e}throw ti=t,ei}}var ti=null;function ud(){if(ti===null)throw Error(c(459));var e=ti;return ti=null,e}function sd(e){if(e===ei||e===ho)throw Error(c(483))}var On=!1;function Sc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function wc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function _n(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function jn(e,t,n){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(ze&2)!==0){var i=l.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),l.pending=t,t=oo(e),Pf(e,null,n),t}return io(e,l,t,n),oo(e)}function ni(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,nf(e,n)}}function Ec(e,t){var n=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var i=null,r=null;if(n=n.firstBaseUpdate,n!==null){do{var f={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};r===null?i=r=f:r=r.next=f,n=n.next}while(n!==null);r===null?i=r=t:r=r.next=t}else i=r=t;n={baseState:l.baseState,firstBaseUpdate:i,lastBaseUpdate:r,shared:l.shared,callbacks:l.callbacks},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Tc=!1;function li(){if(Tc){var e=Pl;if(e!==null)throw e}}function ai(e,t,n,l){Tc=!1;var i=e.updateQueue;On=!1;var r=i.firstBaseUpdate,f=i.lastBaseUpdate,p=i.shared.pending;if(p!==null){i.shared.pending=null;var S=p,j=S.next;S.next=null,f===null?r=j:f.next=j,f=S;var q=e.alternate;q!==null&&(q=q.updateQueue,p=q.lastBaseUpdate,p!==f&&(p===null?q.firstBaseUpdate=j:p.next=j,q.lastBaseUpdate=S))}if(r!==null){var X=i.baseState;f=0,q=j=S=null,p=r;do{var U=p.lane&-536870913,H=U!==p.lane;if(H?(Ne&U)===U:(l&U)===U){U!==0&&U===$l&&(Tc=!0),q!==null&&(q=q.next={lane:0,tag:p.tag,payload:p.payload,callback:null,next:null});e:{var de=e,re=p;U=t;var Le=n;switch(re.tag){case 1:if(de=re.payload,typeof de=="function"){X=de.call(Le,X,U);break e}X=de;break e;case 3:de.flags=de.flags&-65537|128;case 0:if(de=re.payload,U=typeof de=="function"?de.call(Le,X,U):de,U==null)break e;X=x({},X,U);break e;case 2:On=!0}}U=p.callback,U!==null&&(e.flags|=64,H&&(e.flags|=8192),H=i.callbacks,H===null?i.callbacks=[U]:H.push(U))}else H={lane:U,tag:p.tag,payload:p.payload,callback:p.callback,next:null},q===null?(j=q=H,S=X):q=q.next=H,f|=U;if(p=p.next,p===null){if(p=i.shared.pending,p===null)break;H=p,p=H.next,H.next=null,i.lastBaseUpdate=H,i.shared.pending=null}}while(!0);q===null&&(S=X),i.baseState=S,i.firstBaseUpdate=j,i.lastBaseUpdate=q,r===null&&(i.shared.lanes=0),Gn|=f,e.lanes=f,e.memoizedState=X}}function fd(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function dd(e,t){var n=e.callbacks;if(n!==null)for(e.callbacks=null,e=0;e<n.length;e++)fd(n[e],t)}var Fl=V(null),po=V(0);function md(e,t){e=wn,J(po,e),J(Fl,t),wn=e|t.baseLanes}function Ac(){J(po,wn),J(Fl,Fl.current)}function Nc(){wn=po.current,F(Fl),F(po)}var zn=0,be=null,He=null,$e=null,go=!1,Wl=!1,bl=!1,yo=0,ii=0,Il=null,T0=0;function Ke(){throw Error(c(321))}function Rc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!St(e[n],t[n]))return!1;return!0}function Cc(e,t,n,l,i,r){return zn=r,be=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,M.H=e===null||e.memoizedState===null?Pd:Fd,bl=!1,r=n(l,i),bl=!1,Wl&&(r=vd(t,n,l,i)),hd(e),r}function hd(e){M.H=To;var t=He!==null&&He.next!==null;if(zn=0,$e=He=be=null,go=!1,ii=0,Il=null,t)throw Error(c(300));e===null||tt||(e=e.dependencies,e!==null&&so(e)&&(tt=!0))}function vd(e,t,n,l){be=e;var i=0;do{if(Wl&&(Il=null),ii=0,Wl=!1,25<=i)throw Error(c(301));if(i+=1,$e=He=null,e.updateQueue!=null){var r=e.updateQueue;r.lastEffect=null,r.events=null,r.stores=null,r.memoCache!=null&&(r.memoCache.index=0)}M.H=O0,r=t(n,l)}while(Wl);return r}function A0(){var e=M.H,t=e.useState()[0];return t=typeof t.then=="function"?oi(t):t,e=e.useState()[0],(He!==null?He.memoizedState:null)!==e&&(be.flags|=1024),t}function Dc(){var e=yo!==0;return yo=0,e}function Mc(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Oc(e){if(go){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}go=!1}zn=0,$e=He=be=null,Wl=!1,ii=yo=0,Il=null}function pt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return $e===null?be.memoizedState=$e=e:$e=$e.next=e,$e}function Pe(){if(He===null){var e=be.alternate;e=e!==null?e.memoizedState:null}else e=He.next;var t=$e===null?be.memoizedState:$e.next;if(t!==null)$e=t,He=e;else{if(e===null)throw be.alternate===null?Error(c(467)):Error(c(310));He=e,e={memoizedState:He.memoizedState,baseState:He.baseState,baseQueue:He.baseQueue,queue:He.queue,next:null},$e===null?be.memoizedState=$e=e:$e=$e.next=e}return $e}function _c(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function oi(e){var t=ii;return ii+=1,Il===null&&(Il=[]),e=cd(Il,e,t),t=be,($e===null?t.memoizedState:$e.next)===null&&(t=t.alternate,M.H=t===null||t.memoizedState===null?Pd:Fd),e}function bo(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return oi(e);if(e.$$typeof===_)return ut(e)}throw Error(c(438,String(e)))}function jc(e){var t=null,n=be.updateQueue;if(n!==null&&(t=n.memoCache),t==null){var l=be.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(i){return i.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),n===null&&(n=_c(),be.updateQueue=n),n.memoCache=t,n=t.data[t.index],n===void 0)for(n=t.data[t.index]=Array(e),l=0;l<e;l++)n[l]=ve;return t.index++,n}function pn(e,t){return typeof t=="function"?t(e):t}function xo(e){var t=Pe();return zc(t,He,e)}function zc(e,t,n){var l=e.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=n;var i=e.baseQueue,r=l.pending;if(r!==null){if(i!==null){var f=i.next;i.next=r.next,r.next=f}t.baseQueue=i=r,l.pending=null}if(r=e.baseState,i===null)e.memoizedState=r;else{t=i.next;var p=f=null,S=null,j=t,q=!1;do{var X=j.lane&-536870913;if(X!==j.lane?(Ne&X)===X:(zn&X)===X){var U=j.revertLane;if(U===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:j.action,hasEagerState:j.hasEagerState,eagerState:j.eagerState,next:null}),X===$l&&(q=!0);else if((zn&U)===U){j=j.next,U===$l&&(q=!0);continue}else X={lane:0,revertLane:j.revertLane,action:j.action,hasEagerState:j.hasEagerState,eagerState:j.eagerState,next:null},S===null?(p=S=X,f=r):S=S.next=X,be.lanes|=U,Gn|=U;X=j.action,bl&&n(r,X),r=j.hasEagerState?j.eagerState:n(r,X)}else U={lane:X,revertLane:j.revertLane,action:j.action,hasEagerState:j.hasEagerState,eagerState:j.eagerState,next:null},S===null?(p=S=U,f=r):S=S.next=U,be.lanes|=X,Gn|=X;j=j.next}while(j!==null&&j!==t);if(S===null?f=r:S.next=p,!St(r,e.memoizedState)&&(tt=!0,q&&(n=Pl,n!==null)))throw n;e.memoizedState=r,e.baseState=f,e.baseQueue=S,l.lastRenderedState=r}return i===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Uc(e){var t=Pe(),n=t.queue;if(n===null)throw Error(c(311));n.lastRenderedReducer=e;var l=n.dispatch,i=n.pending,r=t.memoizedState;if(i!==null){n.pending=null;var f=i=i.next;do r=e(r,f.action),f=f.next;while(f!==i);St(r,t.memoizedState)||(tt=!0),t.memoizedState=r,t.baseQueue===null&&(t.baseState=r),n.lastRenderedState=r}return[r,l]}function pd(e,t,n){var l=be,i=Pe(),r=Oe;if(r){if(n===void 0)throw Error(c(407));n=n()}else n=t();var f=!St((He||i).memoizedState,n);f&&(i.memoizedState=n,tt=!0),i=i.queue;var p=bd.bind(null,l,i,e);if(ri(2048,8,p,[e]),i.getSnapshot!==t||f||$e!==null&&$e.memoizedState.tag&1){if(l.flags|=2048,ea(9,So(),yd.bind(null,l,i,n,t),null),qe===null)throw Error(c(349));r||(zn&124)!==0||gd(l,t,n)}return n}function gd(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=be.updateQueue,t===null?(t=_c(),be.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function yd(e,t,n,l){t.value=n,t.getSnapshot=l,xd(t)&&Sd(e)}function bd(e,t,n){return n(function(){xd(t)&&Sd(e)})}function xd(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!St(e,n)}catch{return!0}}function Sd(e){var t=Ql(e,2);t!==null&&Rt(t,e,2)}function Hc(e){var t=pt();if(typeof e=="function"){var n=e;if(e=n(),bl){Rn(!0);try{n()}finally{Rn(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:e},t}function wd(e,t,n,l){return e.baseState=n,zc(e,He,typeof l=="function"?l:pn)}function N0(e,t,n,l,i){if(Eo(e))throw Error(c(485));if(e=t.action,e!==null){var r={payload:i,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){r.listeners.push(f)}};M.T!==null?n(!0):r.isTransition=!1,l(r),n=t.pending,n===null?(r.next=t.pending=r,Ed(t,r)):(r.next=n.next,t.pending=n.next=r)}}function Ed(e,t){var n=t.action,l=t.payload,i=e.state;if(t.isTransition){var r=M.T,f={};M.T=f;try{var p=n(i,l),S=M.S;S!==null&&S(f,p),Td(e,t,p)}catch(j){Bc(e,t,j)}finally{M.T=r}}else try{r=n(i,l),Td(e,t,r)}catch(j){Bc(e,t,j)}}function Td(e,t,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Ad(e,t,l)},function(l){return Bc(e,t,l)}):Ad(e,t,n)}function Ad(e,t,n){t.status="fulfilled",t.value=n,Nd(t),e.state=n,t=e.pending,t!==null&&(n=t.next,n===t?e.pending=null:(n=n.next,t.next=n,Ed(e,n)))}function Bc(e,t,n){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=n,Nd(t),t=t.next;while(t!==l)}e.action=null}function Nd(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Rd(e,t){return t}function Cd(e,t){if(Oe){var n=qe.formState;if(n!==null){e:{var l=be;if(Oe){if(Xe){t:{for(var i=Xe,r=Wt;i.nodeType!==8;){if(!r){i=null;break t}if(i=Vt(i.nextSibling),i===null){i=null;break t}}r=i.data,i=r==="F!"||r==="F"?i:null}if(i){Xe=Vt(i.nextSibling),l=i.data==="F!";break e}}vl(l)}l=!1}l&&(t=n[0])}}return n=pt(),n.memoizedState=n.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Rd,lastRenderedState:t},n.queue=l,n=Kd.bind(null,be,l),l.dispatch=n,l=Hc(!1),r=Yc.bind(null,be,!1,l.queue),l=pt(),i={state:t,dispatch:null,action:e,pending:null},l.queue=i,n=N0.bind(null,be,i,r,n),i.dispatch=n,l.memoizedState=e,[t,n,!1]}function Dd(e){var t=Pe();return Md(t,He,e)}function Md(e,t,n){if(t=zc(e,t,Rd)[0],e=xo(pn)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=oi(t)}catch(f){throw f===ei?ho:f}else l=t;t=Pe();var i=t.queue,r=i.dispatch;return n!==t.memoizedState&&(be.flags|=2048,ea(9,So(),R0.bind(null,i,n),null)),[l,r,e]}function R0(e,t){e.action=t}function Od(e){var t=Pe(),n=He;if(n!==null)return Md(t,n,e);Pe(),t=t.memoizedState,n=Pe();var l=n.queue.dispatch;return n.memoizedState=e,[t,l,!1]}function ea(e,t,n,l){return e={tag:e,create:n,deps:l,inst:t,next:null},t=be.updateQueue,t===null&&(t=_c(),be.updateQueue=t),n=t.lastEffect,n===null?t.lastEffect=e.next=e:(l=n.next,n.next=e,e.next=l,t.lastEffect=e),e}function So(){return{destroy:void 0,resource:void 0}}function _d(){return Pe().memoizedState}function wo(e,t,n,l){var i=pt();l=l===void 0?null:l,be.flags|=e,i.memoizedState=ea(1|t,So(),n,l)}function ri(e,t,n,l){var i=Pe();l=l===void 0?null:l;var r=i.memoizedState.inst;He!==null&&l!==null&&Rc(l,He.memoizedState.deps)?i.memoizedState=ea(t,r,n,l):(be.flags|=e,i.memoizedState=ea(1|t,r,n,l))}function jd(e,t){wo(8390656,8,e,t)}function zd(e,t){ri(2048,8,e,t)}function Ud(e,t){return ri(4,2,e,t)}function Hd(e,t){return ri(4,4,e,t)}function Bd(e,t){if(typeof t=="function"){e=e();var n=t(e);return function(){typeof n=="function"?n():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ld(e,t,n){n=n!=null?n.concat([e]):null,ri(4,4,Bd.bind(null,t,e),n)}function Lc(){}function kd(e,t){var n=Pe();t=t===void 0?null:t;var l=n.memoizedState;return t!==null&&Rc(t,l[1])?l[0]:(n.memoizedState=[e,t],e)}function qd(e,t){var n=Pe();t=t===void 0?null:t;var l=n.memoizedState;if(t!==null&&Rc(t,l[1]))return l[0];if(l=e(),bl){Rn(!0);try{e()}finally{Rn(!1)}}return n.memoizedState=[l,t],l}function kc(e,t,n){return n===void 0||(zn&1073741824)!==0?e.memoizedState=t:(e.memoizedState=n,e=Vm(),be.lanes|=e,Gn|=e,n)}function Gd(e,t,n,l){return St(n,t)?n:Fl.current!==null?(e=kc(e,n,l),St(e,t)||(tt=!0),e):(zn&42)===0?(tt=!0,e.memoizedState=n):(e=Vm(),be.lanes|=e,Gn|=e,t)}function Yd(e,t,n,l,i){var r=Q.p;Q.p=r!==0&&8>r?r:8;var f=M.T,p={};M.T=p,Yc(e,!1,t,n);try{var S=i(),j=M.S;if(j!==null&&j(p,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var q=E0(S,l);ci(e,t,q,Nt(e))}else ci(e,t,l,Nt(e))}catch(X){ci(e,t,{then:function(){},status:"rejected",reason:X},Nt())}finally{Q.p=r,M.T=f}}function C0(){}function qc(e,t,n,l){if(e.tag!==5)throw Error(c(476));var i=Vd(e).queue;Yd(e,i,t,B,n===null?C0:function(){return Xd(e),n(l)})}function Vd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:B,baseState:B,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:B},next:null};var n={};return t.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:pn,lastRenderedState:n},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Xd(e){var t=Vd(e).next.queue;ci(e,t,{},Nt())}function Gc(){return ut(Ni)}function Qd(){return Pe().memoizedState}function Zd(){return Pe().memoizedState}function D0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var n=Nt();e=_n(n);var l=jn(t,e,n);l!==null&&(Rt(l,t,n),ni(l,t,n)),t={cache:gc()},e.payload=t;return}t=t.return}}function M0(e,t,n){var l=Nt();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Eo(e)?Jd(t,n):(n=rc(e,t,n,l),n!==null&&(Rt(n,e,l),$d(n,t,l)))}function Kd(e,t,n){var l=Nt();ci(e,t,n,l)}function ci(e,t,n,l){var i={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Eo(e))Jd(t,i);else{var r=e.alternate;if(e.lanes===0&&(r===null||r.lanes===0)&&(r=t.lastRenderedReducer,r!==null))try{var f=t.lastRenderedState,p=r(f,n);if(i.hasEagerState=!0,i.eagerState=p,St(p,f))return io(e,t,i,0),qe===null&&ao(),!1}catch{}finally{}if(n=rc(e,t,i,l),n!==null)return Rt(n,e,l),$d(n,t,l),!0}return!1}function Yc(e,t,n,l){if(l={lane:2,revertLane:xu(),action:l,hasEagerState:!1,eagerState:null,next:null},Eo(e)){if(t)throw Error(c(479))}else t=rc(e,n,l,2),t!==null&&Rt(t,e,2)}function Eo(e){var t=e.alternate;return e===be||t!==null&&t===be}function Jd(e,t){Wl=go=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function $d(e,t,n){if((n&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,nf(e,n)}}var To={readContext:ut,use:bo,useCallback:Ke,useContext:Ke,useEffect:Ke,useImperativeHandle:Ke,useLayoutEffect:Ke,useInsertionEffect:Ke,useMemo:Ke,useReducer:Ke,useRef:Ke,useState:Ke,useDebugValue:Ke,useDeferredValue:Ke,useTransition:Ke,useSyncExternalStore:Ke,useId:Ke,useHostTransitionStatus:Ke,useFormState:Ke,useActionState:Ke,useOptimistic:Ke,useMemoCache:Ke,useCacheRefresh:Ke},Pd={readContext:ut,use:bo,useCallback:function(e,t){return pt().memoizedState=[e,t===void 0?null:t],e},useContext:ut,useEffect:jd,useImperativeHandle:function(e,t,n){n=n!=null?n.concat([e]):null,wo(4194308,4,Bd.bind(null,t,e),n)},useLayoutEffect:function(e,t){return wo(4194308,4,e,t)},useInsertionEffect:function(e,t){wo(4,2,e,t)},useMemo:function(e,t){var n=pt();t=t===void 0?null:t;var l=e();if(bl){Rn(!0);try{e()}finally{Rn(!1)}}return n.memoizedState=[l,t],l},useReducer:function(e,t,n){var l=pt();if(n!==void 0){var i=n(t);if(bl){Rn(!0);try{n(t)}finally{Rn(!1)}}}else i=t;return l.memoizedState=l.baseState=i,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:i},l.queue=e,e=e.dispatch=M0.bind(null,be,e),[l.memoizedState,e]},useRef:function(e){var t=pt();return e={current:e},t.memoizedState=e},useState:function(e){e=Hc(e);var t=e.queue,n=Kd.bind(null,be,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Lc,useDeferredValue:function(e,t){var n=pt();return kc(n,e,t)},useTransition:function(){var e=Hc(!1);return e=Yd.bind(null,be,e.queue,!0,!1),pt().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var l=be,i=pt();if(Oe){if(n===void 0)throw Error(c(407));n=n()}else{if(n=t(),qe===null)throw Error(c(349));(Ne&124)!==0||gd(l,t,n)}i.memoizedState=n;var r={value:n,getSnapshot:t};return i.queue=r,jd(bd.bind(null,l,r,e),[e]),l.flags|=2048,ea(9,So(),yd.bind(null,l,r,n,t),null),n},useId:function(){var e=pt(),t=qe.identifierPrefix;if(Oe){var n=mn,l=dn;n=(l&~(1<<32-xt(l)-1)).toString(32)+n,t="«"+t+"R"+n,n=yo++,0<n&&(t+="H"+n.toString(32)),t+="»"}else n=T0++,t="«"+t+"r"+n.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Gc,useFormState:Cd,useActionState:Cd,useOptimistic:function(e){var t=pt();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Yc.bind(null,be,!0,n),n.dispatch=t,[e,t]},useMemoCache:jc,useCacheRefresh:function(){return pt().memoizedState=D0.bind(null,be)}},Fd={readContext:ut,use:bo,useCallback:kd,useContext:ut,useEffect:zd,useImperativeHandle:Ld,useInsertionEffect:Ud,useLayoutEffect:Hd,useMemo:qd,useReducer:xo,useRef:_d,useState:function(){return xo(pn)},useDebugValue:Lc,useDeferredValue:function(e,t){var n=Pe();return Gd(n,He.memoizedState,e,t)},useTransition:function(){var e=xo(pn)[0],t=Pe().memoizedState;return[typeof e=="boolean"?e:oi(e),t]},useSyncExternalStore:pd,useId:Qd,useHostTransitionStatus:Gc,useFormState:Dd,useActionState:Dd,useOptimistic:function(e,t){var n=Pe();return wd(n,He,e,t)},useMemoCache:jc,useCacheRefresh:Zd},O0={readContext:ut,use:bo,useCallback:kd,useContext:ut,useEffect:zd,useImperativeHandle:Ld,useInsertionEffect:Ud,useLayoutEffect:Hd,useMemo:qd,useReducer:Uc,useRef:_d,useState:function(){return Uc(pn)},useDebugValue:Lc,useDeferredValue:function(e,t){var n=Pe();return He===null?kc(n,e,t):Gd(n,He.memoizedState,e,t)},useTransition:function(){var e=Uc(pn)[0],t=Pe().memoizedState;return[typeof e=="boolean"?e:oi(e),t]},useSyncExternalStore:pd,useId:Qd,useHostTransitionStatus:Gc,useFormState:Od,useActionState:Od,useOptimistic:function(e,t){var n=Pe();return He!==null?wd(n,He,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:jc,useCacheRefresh:Zd},ta=null,ui=0;function Ao(e){var t=ui;return ui+=1,ta===null&&(ta=[]),cd(ta,e,t)}function si(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function No(e,t){throw t.$$typeof===w?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Wd(e){var t=e._init;return t(e._payload)}function Id(e){function t(C,A){if(e){var O=C.deletions;O===null?(C.deletions=[A],C.flags|=16):O.push(A)}}function n(C,A){if(!e)return null;for(;A!==null;)t(C,A),A=A.sibling;return null}function l(C){for(var A=new Map;C!==null;)C.key!==null?A.set(C.key,C):A.set(C.index,C),C=C.sibling;return A}function i(C,A){return C=fn(C,A),C.index=0,C.sibling=null,C}function r(C,A,O){return C.index=O,e?(O=C.alternate,O!==null?(O=O.index,O<A?(C.flags|=67108866,A):O):(C.flags|=67108866,A)):(C.flags|=1048576,A)}function f(C){return e&&C.alternate===null&&(C.flags|=67108866),C}function p(C,A,O,Y){return A===null||A.tag!==6?(A=uc(O,C.mode,Y),A.return=C,A):(A=i(A,O),A.return=C,A)}function S(C,A,O,Y){var ee=O.type;return ee===z?q(C,A,O.props.children,Y,O.key):A!==null&&(A.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===K&&Wd(ee)===A.type)?(A=i(A,O.props),si(A,O),A.return=C,A):(A=ro(O.type,O.key,O.props,null,C.mode,Y),si(A,O),A.return=C,A)}function j(C,A,O,Y){return A===null||A.tag!==4||A.stateNode.containerInfo!==O.containerInfo||A.stateNode.implementation!==O.implementation?(A=sc(O,C.mode,Y),A.return=C,A):(A=i(A,O.children||[]),A.return=C,A)}function q(C,A,O,Y,ee){return A===null||A.tag!==7?(A=fl(O,C.mode,Y,ee),A.return=C,A):(A=i(A,O),A.return=C,A)}function X(C,A,O){if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return A=uc(""+A,C.mode,O),A.return=C,A;if(typeof A=="object"&&A!==null){switch(A.$$typeof){case R:return O=ro(A.type,A.key,A.props,null,C.mode,O),si(O,A),O.return=C,O;case N:return A=sc(A,C.mode,O),A.return=C,A;case K:var Y=A._init;return A=Y(A._payload),X(C,A,O)}if(fe(A)||se(A))return A=fl(A,C.mode,O,null),A.return=C,A;if(typeof A.then=="function")return X(C,Ao(A),O);if(A.$$typeof===_)return X(C,fo(C,A),O);No(C,A)}return null}function U(C,A,O,Y){var ee=A!==null?A.key:null;if(typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint")return ee!==null?null:p(C,A,""+O,Y);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case R:return O.key===ee?S(C,A,O,Y):null;case N:return O.key===ee?j(C,A,O,Y):null;case K:return ee=O._init,O=ee(O._payload),U(C,A,O,Y)}if(fe(O)||se(O))return ee!==null?null:q(C,A,O,Y,null);if(typeof O.then=="function")return U(C,A,Ao(O),Y);if(O.$$typeof===_)return U(C,A,fo(C,O),Y);No(C,O)}return null}function H(C,A,O,Y,ee){if(typeof Y=="string"&&Y!==""||typeof Y=="number"||typeof Y=="bigint")return C=C.get(O)||null,p(A,C,""+Y,ee);if(typeof Y=="object"&&Y!==null){switch(Y.$$typeof){case R:return C=C.get(Y.key===null?O:Y.key)||null,S(A,C,Y,ee);case N:return C=C.get(Y.key===null?O:Y.key)||null,j(A,C,Y,ee);case K:var Se=Y._init;return Y=Se(Y._payload),H(C,A,O,Y,ee)}if(fe(Y)||se(Y))return C=C.get(O)||null,q(A,C,Y,ee,null);if(typeof Y.then=="function")return H(C,A,O,Ao(Y),ee);if(Y.$$typeof===_)return H(C,A,O,fo(A,Y),ee);No(A,Y)}return null}function de(C,A,O,Y){for(var ee=null,Se=null,le=A,ue=A=0,lt=null;le!==null&&ue<O.length;ue++){le.index>ue?(lt=le,le=null):lt=le.sibling;var Me=U(C,le,O[ue],Y);if(Me===null){le===null&&(le=lt);break}e&&le&&Me.alternate===null&&t(C,le),A=r(Me,A,ue),Se===null?ee=Me:Se.sibling=Me,Se=Me,le=lt}if(ue===O.length)return n(C,le),Oe&&ml(C,ue),ee;if(le===null){for(;ue<O.length;ue++)le=X(C,O[ue],Y),le!==null&&(A=r(le,A,ue),Se===null?ee=le:Se.sibling=le,Se=le);return Oe&&ml(C,ue),ee}for(le=l(le);ue<O.length;ue++)lt=H(le,C,ue,O[ue],Y),lt!==null&&(e&&lt.alternate!==null&&le.delete(lt.key===null?ue:lt.key),A=r(lt,A,ue),Se===null?ee=lt:Se.sibling=lt,Se=lt);return e&&le.forEach(function(Pn){return t(C,Pn)}),Oe&&ml(C,ue),ee}function re(C,A,O,Y){if(O==null)throw Error(c(151));for(var ee=null,Se=null,le=A,ue=A=0,lt=null,Me=O.next();le!==null&&!Me.done;ue++,Me=O.next()){le.index>ue?(lt=le,le=null):lt=le.sibling;var Pn=U(C,le,Me.value,Y);if(Pn===null){le===null&&(le=lt);break}e&&le&&Pn.alternate===null&&t(C,le),A=r(Pn,A,ue),Se===null?ee=Pn:Se.sibling=Pn,Se=Pn,le=lt}if(Me.done)return n(C,le),Oe&&ml(C,ue),ee;if(le===null){for(;!Me.done;ue++,Me=O.next())Me=X(C,Me.value,Y),Me!==null&&(A=r(Me,A,ue),Se===null?ee=Me:Se.sibling=Me,Se=Me);return Oe&&ml(C,ue),ee}for(le=l(le);!Me.done;ue++,Me=O.next())Me=H(le,C,ue,Me.value,Y),Me!==null&&(e&&Me.alternate!==null&&le.delete(Me.key===null?ue:Me.key),A=r(Me,A,ue),Se===null?ee=Me:Se.sibling=Me,Se=Me);return e&&le.forEach(function(_b){return t(C,_b)}),Oe&&ml(C,ue),ee}function Le(C,A,O,Y){if(typeof O=="object"&&O!==null&&O.type===z&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case R:e:{for(var ee=O.key;A!==null;){if(A.key===ee){if(ee=O.type,ee===z){if(A.tag===7){n(C,A.sibling),Y=i(A,O.props.children),Y.return=C,C=Y;break e}}else if(A.elementType===ee||typeof ee=="object"&&ee!==null&&ee.$$typeof===K&&Wd(ee)===A.type){n(C,A.sibling),Y=i(A,O.props),si(Y,O),Y.return=C,C=Y;break e}n(C,A);break}else t(C,A);A=A.sibling}O.type===z?(Y=fl(O.props.children,C.mode,Y,O.key),Y.return=C,C=Y):(Y=ro(O.type,O.key,O.props,null,C.mode,Y),si(Y,O),Y.return=C,C=Y)}return f(C);case N:e:{for(ee=O.key;A!==null;){if(A.key===ee)if(A.tag===4&&A.stateNode.containerInfo===O.containerInfo&&A.stateNode.implementation===O.implementation){n(C,A.sibling),Y=i(A,O.children||[]),Y.return=C,C=Y;break e}else{n(C,A);break}else t(C,A);A=A.sibling}Y=sc(O,C.mode,Y),Y.return=C,C=Y}return f(C);case K:return ee=O._init,O=ee(O._payload),Le(C,A,O,Y)}if(fe(O))return de(C,A,O,Y);if(se(O)){if(ee=se(O),typeof ee!="function")throw Error(c(150));return O=ee.call(O),re(C,A,O,Y)}if(typeof O.then=="function")return Le(C,A,Ao(O),Y);if(O.$$typeof===_)return Le(C,A,fo(C,O),Y);No(C,O)}return typeof O=="string"&&O!==""||typeof O=="number"||typeof O=="bigint"?(O=""+O,A!==null&&A.tag===6?(n(C,A.sibling),Y=i(A,O),Y.return=C,C=Y):(n(C,A),Y=uc(O,C.mode,Y),Y.return=C,C=Y),f(C)):n(C,A)}return function(C,A,O,Y){try{ui=0;var ee=Le(C,A,O,Y);return ta=null,ee}catch(le){if(le===ei||le===ho)throw le;var Se=wt(29,le,null,C.mode);return Se.lanes=Y,Se.return=C,Se}finally{}}}var na=Id(!0),em=Id(!1),Ht=V(null),It=null;function Un(e){var t=e.alternate;J(We,We.current&1),J(Ht,e),It===null&&(t===null||Fl.current!==null||t.memoizedState!==null)&&(It=e)}function tm(e){if(e.tag===22){if(J(We,We.current),J(Ht,e),It===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(It=e)}}else Hn()}function Hn(){J(We,We.current),J(Ht,Ht.current)}function gn(e){F(Ht),It===e&&(It=null),F(We)}var We=V(0);function Ro(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||_u(n)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Vc(e,t,n,l){t=e.memoizedState,n=n(l,t),n=n==null?t:x({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Xc={enqueueSetState:function(e,t,n){e=e._reactInternals;var l=Nt(),i=_n(l);i.payload=t,n!=null&&(i.callback=n),t=jn(e,i,l),t!==null&&(Rt(t,e,l),ni(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var l=Nt(),i=_n(l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=jn(e,i,l),t!==null&&(Rt(t,e,l),ni(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Nt(),l=_n(n);l.tag=2,t!=null&&(l.callback=t),t=jn(e,l,n),t!==null&&(Rt(t,e,n),ni(t,e,n))}};function nm(e,t,n,l,i,r,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,r,f):t.prototype&&t.prototype.isPureReactComponent?!Za(n,l)||!Za(i,r):!0}function lm(e,t,n,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,l),t.state!==e&&Xc.enqueueReplaceState(t,t.state,null)}function xl(e,t){var n=t;if("ref"in t){n={};for(var l in t)l!=="ref"&&(n[l]=t[l])}if(e=e.defaultProps){n===t&&(n=x({},n));for(var i in e)n[i]===void 0&&(n[i]=e[i])}return n}var Co=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function am(e){Co(e)}function im(e){console.error(e)}function om(e){Co(e)}function Do(e,t){try{var n=e.onUncaughtError;n(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function rm(e,t,n){try{var l=e.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function Qc(e,t,n){return n=_n(n),n.tag=3,n.payload={element:null},n.callback=function(){Do(e,t)},n}function cm(e){return e=_n(e),e.tag=3,e}function um(e,t,n,l){var i=n.type.getDerivedStateFromError;if(typeof i=="function"){var r=l.value;e.payload=function(){return i(r)},e.callback=function(){rm(t,n,l)}}var f=n.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){rm(t,n,l),typeof i!="function"&&(Yn===null?Yn=new Set([this]):Yn.add(this));var p=l.stack;this.componentDidCatch(l.value,{componentStack:p!==null?p:""})})}function _0(e,t,n,l,i){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=n.alternate,t!==null&&Fa(t,n,i,!0),n=Ht.current,n!==null){switch(n.tag){case 13:return It===null?vu():n.alternate===null&&Qe===0&&(Qe=3),n.flags&=-257,n.flags|=65536,n.lanes=i,l===xc?n.flags|=16384:(t=n.updateQueue,t===null?n.updateQueue=new Set([l]):t.add(l),gu(e,l,i)),!1;case 22:return n.flags|=65536,l===xc?n.flags|=16384:(t=n.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=t):(n=t.retryQueue,n===null?t.retryQueue=new Set([l]):n.add(l)),gu(e,l,i)),!1}throw Error(c(435,n.tag))}return gu(e,l,i),vu(),!1}if(Oe)return t=Ht.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=i,l!==mc&&(e=Error(c(422),{cause:l}),Pa(_t(e,n)))):(l!==mc&&(t=Error(c(423),{cause:l}),Pa(_t(t,n))),e=e.current.alternate,e.flags|=65536,i&=-i,e.lanes|=i,l=_t(l,n),i=Qc(e.stateNode,l,i),Ec(e,i),Qe!==4&&(Qe=2)),!1;var r=Error(c(520),{cause:l});if(r=_t(r,n),gi===null?gi=[r]:gi.push(r),Qe!==4&&(Qe=2),t===null)return!0;l=_t(l,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=i&-i,n.lanes|=e,e=Qc(n.stateNode,l,e),Ec(n,e),!1;case 1:if(t=n.type,r=n.stateNode,(n.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||r!==null&&typeof r.componentDidCatch=="function"&&(Yn===null||!Yn.has(r))))return n.flags|=65536,i&=-i,n.lanes|=i,i=cm(i),um(i,e,n,l),Ec(n,i),!1}n=n.return}while(n!==null);return!1}var sm=Error(c(461)),tt=!1;function at(e,t,n,l){t.child=e===null?em(t,null,n,l):na(t,e.child,n,l)}function fm(e,t,n,l,i){n=n.render;var r=t.ref;if("ref"in l){var f={};for(var p in l)p!=="ref"&&(f[p]=l[p])}else f=l;return gl(t),l=Cc(e,t,n,f,r,i),p=Dc(),e!==null&&!tt?(Mc(e,t,i),yn(e,t,i)):(Oe&&p&&fc(t),t.flags|=1,at(e,t,l,i),t.child)}function dm(e,t,n,l,i){if(e===null){var r=n.type;return typeof r=="function"&&!cc(r)&&r.defaultProps===void 0&&n.compare===null?(t.tag=15,t.type=r,mm(e,t,r,l,i)):(e=ro(n.type,null,l,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(r=e.child,!Ic(e,i)){var f=r.memoizedProps;if(n=n.compare,n=n!==null?n:Za,n(f,l)&&e.ref===t.ref)return yn(e,t,i)}return t.flags|=1,e=fn(r,l),e.ref=t.ref,e.return=t,t.child=e}function mm(e,t,n,l,i){if(e!==null){var r=e.memoizedProps;if(Za(r,l)&&e.ref===t.ref)if(tt=!1,t.pendingProps=l=r,Ic(e,i))(e.flags&131072)!==0&&(tt=!0);else return t.lanes=e.lanes,yn(e,t,i)}return Zc(e,t,n,l,i)}function hm(e,t,n){var l=t.pendingProps,i=l.children,r=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=r!==null?r.baseLanes|n:n,e!==null){for(i=t.child=e.child,r=0;i!==null;)r=r|i.lanes|i.childLanes,i=i.sibling;t.childLanes=r&~l}else t.childLanes=0,t.child=null;return vm(e,t,l,n)}if((n&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&mo(t,r!==null?r.cachePool:null),r!==null?md(t,r):Ac(),tm(t);else return t.lanes=t.childLanes=536870912,vm(e,t,r!==null?r.baseLanes|n:n,n)}else r!==null?(mo(t,r.cachePool),md(t,r),Hn(),t.memoizedState=null):(e!==null&&mo(t,null),Ac(),Hn());return at(e,t,i,n),t.child}function vm(e,t,n,l){var i=bc();return i=i===null?null:{parent:Fe._currentValue,pool:i},t.memoizedState={baseLanes:n,cachePool:i},e!==null&&mo(t,null),Ac(),tm(t),e!==null&&Fa(e,t,l,!0),null}function Mo(e,t){var n=t.ref;if(n===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(c(284));(e===null||e.ref!==n)&&(t.flags|=4194816)}}function Zc(e,t,n,l,i){return gl(t),n=Cc(e,t,n,l,void 0,i),l=Dc(),e!==null&&!tt?(Mc(e,t,i),yn(e,t,i)):(Oe&&l&&fc(t),t.flags|=1,at(e,t,n,i),t.child)}function pm(e,t,n,l,i,r){return gl(t),t.updateQueue=null,n=vd(t,l,n,i),hd(e),l=Dc(),e!==null&&!tt?(Mc(e,t,r),yn(e,t,r)):(Oe&&l&&fc(t),t.flags|=1,at(e,t,n,r),t.child)}function gm(e,t,n,l,i){if(gl(t),t.stateNode===null){var r=Zl,f=n.contextType;typeof f=="object"&&f!==null&&(r=ut(f)),r=new n(l,r),t.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,r.updater=Xc,t.stateNode=r,r._reactInternals=t,r=t.stateNode,r.props=l,r.state=t.memoizedState,r.refs={},Sc(t),f=n.contextType,r.context=typeof f=="object"&&f!==null?ut(f):Zl,r.state=t.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(Vc(t,n,f,l),r.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(f=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),f!==r.state&&Xc.enqueueReplaceState(r,r.state,null),ai(t,l,r,i),li(),r.state=t.memoizedState),typeof r.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){r=t.stateNode;var p=t.memoizedProps,S=xl(n,p);r.props=S;var j=r.context,q=n.contextType;f=Zl,typeof q=="object"&&q!==null&&(f=ut(q));var X=n.getDerivedStateFromProps;q=typeof X=="function"||typeof r.getSnapshotBeforeUpdate=="function",p=t.pendingProps!==p,q||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(p||j!==f)&&lm(t,r,l,f),On=!1;var U=t.memoizedState;r.state=U,ai(t,l,r,i),li(),j=t.memoizedState,p||U!==j||On?(typeof X=="function"&&(Vc(t,n,X,l),j=t.memoizedState),(S=On||nm(t,n,S,l,U,j,f))?(q||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount()),typeof r.componentDidMount=="function"&&(t.flags|=4194308)):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=j),r.props=l,r.state=j,r.context=f,l=S):(typeof r.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{r=t.stateNode,wc(e,t),f=t.memoizedProps,q=xl(n,f),r.props=q,X=t.pendingProps,U=r.context,j=n.contextType,S=Zl,typeof j=="object"&&j!==null&&(S=ut(j)),p=n.getDerivedStateFromProps,(j=typeof p=="function"||typeof r.getSnapshotBeforeUpdate=="function")||typeof r.UNSAFE_componentWillReceiveProps!="function"&&typeof r.componentWillReceiveProps!="function"||(f!==X||U!==S)&&lm(t,r,l,S),On=!1,U=t.memoizedState,r.state=U,ai(t,l,r,i),li();var H=t.memoizedState;f!==X||U!==H||On||e!==null&&e.dependencies!==null&&so(e.dependencies)?(typeof p=="function"&&(Vc(t,n,p,l),H=t.memoizedState),(q=On||nm(t,n,q,l,U,H,S)||e!==null&&e.dependencies!==null&&so(e.dependencies))?(j||typeof r.UNSAFE_componentWillUpdate!="function"&&typeof r.componentWillUpdate!="function"||(typeof r.componentWillUpdate=="function"&&r.componentWillUpdate(l,H,S),typeof r.UNSAFE_componentWillUpdate=="function"&&r.UNSAFE_componentWillUpdate(l,H,S)),typeof r.componentDidUpdate=="function"&&(t.flags|=4),typeof r.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof r.componentDidUpdate!="function"||f===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=H),r.props=l,r.state=H,r.context=S,l=q):(typeof r.componentDidUpdate!="function"||f===e.memoizedProps&&U===e.memoizedState||(t.flags|=4),typeof r.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&U===e.memoizedState||(t.flags|=1024),l=!1)}return r=l,Mo(e,t),l=(t.flags&128)!==0,r||l?(r=t.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:r.render(),t.flags|=1,e!==null&&l?(t.child=na(t,e.child,null,i),t.child=na(t,null,n,i)):at(e,t,n,i),t.memoizedState=r.state,e=t.child):e=yn(e,t,i),e}function ym(e,t,n,l){return $a(),t.flags|=256,at(e,t,n,l),t.child}var Kc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Jc(e){return{baseLanes:e,cachePool:id()}}function $c(e,t,n){return e=e!==null?e.childLanes&~n:0,t&&(e|=Bt),e}function bm(e,t,n){var l=t.pendingProps,i=!1,r=(t.flags&128)!==0,f;if((f=r)||(f=e!==null&&e.memoizedState===null?!1:(We.current&2)!==0),f&&(i=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(Oe){if(i?Un(t):Hn(),Oe){var p=Xe,S;if(S=p){e:{for(S=p,p=Wt;S.nodeType!==8;){if(!p){p=null;break e}if(S=Vt(S.nextSibling),S===null){p=null;break e}}p=S}p!==null?(t.memoizedState={dehydrated:p,treeContext:dl!==null?{id:dn,overflow:mn}:null,retryLane:536870912,hydrationErrors:null},S=wt(18,null,null,0),S.stateNode=p,S.return=t,t.child=S,dt=t,Xe=null,S=!0):S=!1}S||vl(t)}if(p=t.memoizedState,p!==null&&(p=p.dehydrated,p!==null))return _u(p)?t.lanes=32:t.lanes=536870912,null;gn(t)}return p=l.children,l=l.fallback,i?(Hn(),i=t.mode,p=Oo({mode:"hidden",children:p},i),l=fl(l,i,n,null),p.return=t,l.return=t,p.sibling=l,t.child=p,i=t.child,i.memoizedState=Jc(n),i.childLanes=$c(e,f,n),t.memoizedState=Kc,l):(Un(t),Pc(t,p))}if(S=e.memoizedState,S!==null&&(p=S.dehydrated,p!==null)){if(r)t.flags&256?(Un(t),t.flags&=-257,t=Fc(e,t,n)):t.memoizedState!==null?(Hn(),t.child=e.child,t.flags|=128,t=null):(Hn(),i=l.fallback,p=t.mode,l=Oo({mode:"visible",children:l.children},p),i=fl(i,p,n,null),i.flags|=2,l.return=t,i.return=t,l.sibling=i,t.child=l,na(t,e.child,null,n),l=t.child,l.memoizedState=Jc(n),l.childLanes=$c(e,f,n),t.memoizedState=Kc,t=i);else if(Un(t),_u(p)){if(f=p.nextSibling&&p.nextSibling.dataset,f)var j=f.dgst;f=j,l=Error(c(419)),l.stack="",l.digest=f,Pa({value:l,source:null,stack:null}),t=Fc(e,t,n)}else if(tt||Fa(e,t,n,!1),f=(n&e.childLanes)!==0,tt||f){if(f=qe,f!==null&&(l=n&-n,l=(l&42)!==0?1:jr(l),l=(l&(f.suspendedLanes|n))!==0?0:l,l!==0&&l!==S.retryLane))throw S.retryLane=l,Ql(e,l),Rt(f,e,l),sm;p.data==="$?"||vu(),t=Fc(e,t,n)}else p.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=S.treeContext,Xe=Vt(p.nextSibling),dt=t,Oe=!0,hl=null,Wt=!1,e!==null&&(zt[Ut++]=dn,zt[Ut++]=mn,zt[Ut++]=dl,dn=e.id,mn=e.overflow,dl=t),t=Pc(t,l.children),t.flags|=4096);return t}return i?(Hn(),i=l.fallback,p=t.mode,S=e.child,j=S.sibling,l=fn(S,{mode:"hidden",children:l.children}),l.subtreeFlags=S.subtreeFlags&65011712,j!==null?i=fn(j,i):(i=fl(i,p,n,null),i.flags|=2),i.return=t,l.return=t,l.sibling=i,t.child=l,l=i,i=t.child,p=e.child.memoizedState,p===null?p=Jc(n):(S=p.cachePool,S!==null?(j=Fe._currentValue,S=S.parent!==j?{parent:j,pool:j}:S):S=id(),p={baseLanes:p.baseLanes|n,cachePool:S}),i.memoizedState=p,i.childLanes=$c(e,f,n),t.memoizedState=Kc,l):(Un(t),n=e.child,e=n.sibling,n=fn(n,{mode:"visible",children:l.children}),n.return=t,n.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=n,t.memoizedState=null,n)}function Pc(e,t){return t=Oo({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Oo(e,t){return e=wt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Fc(e,t,n){return na(t,e.child,null,n),e=Pc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function xm(e,t,n){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),vc(e.return,t,n)}function Wc(e,t,n,l,i){var r=e.memoizedState;r===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:i}:(r.isBackwards=t,r.rendering=null,r.renderingStartTime=0,r.last=l,r.tail=n,r.tailMode=i)}function Sm(e,t,n){var l=t.pendingProps,i=l.revealOrder,r=l.tail;if(at(e,t,l.children,n),l=We.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&xm(e,n,t);else if(e.tag===19)xm(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(J(We,l),i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Ro(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Wc(t,!1,i,n,r);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Ro(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Wc(t,!0,n,null,r);break;case"together":Wc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function yn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Gn|=t.lanes,(n&t.childLanes)===0)if(e!==null){if(Fa(e,t,n,!1),(n&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,n=fn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=fn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Ic(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&so(e)))}function j0(e,t,n){switch(t.tag){case 3:ce(t,t.stateNode.containerInfo),Mn(t,Fe,e.memoizedState.cache),$a();break;case 27:case 5:Ae(t);break;case 4:ce(t,t.stateNode.containerInfo);break;case 10:Mn(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Un(t),t.flags|=128,null):(n&t.child.childLanes)!==0?bm(e,t,n):(Un(t),e=yn(e,t,n),e!==null?e.sibling:null);Un(t);break;case 19:var i=(e.flags&128)!==0;if(l=(n&t.childLanes)!==0,l||(Fa(e,t,n,!1),l=(n&t.childLanes)!==0),i){if(l)return Sm(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),J(We,We.current),l)break;return null;case 22:case 23:return t.lanes=0,hm(e,t,n);case 24:Mn(t,Fe,e.memoizedState.cache)}return yn(e,t,n)}function wm(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps)tt=!0;else{if(!Ic(e,n)&&(t.flags&128)===0)return tt=!1,j0(e,t,n);tt=(e.flags&131072)!==0}else tt=!1,Oe&&(t.flags&1048576)!==0&&Wf(t,uo,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,i=l._init;if(l=i(l._payload),t.type=l,typeof l=="function")cc(l)?(e=xl(l,e),t.tag=1,t=gm(null,t,l,e,n)):(t.tag=0,t=Zc(null,t,l,e,n));else{if(l!=null){if(i=l.$$typeof,i===Z){t.tag=11,t=fm(null,t,l,e,n);break e}else if(i===$){t.tag=14,t=dm(null,t,l,e,n);break e}}throw t=me(l)||l,Error(c(306,t,""))}}return t;case 0:return Zc(e,t,t.type,t.pendingProps,n);case 1:return l=t.type,i=xl(l,t.pendingProps),gm(e,t,l,i,n);case 3:e:{if(ce(t,t.stateNode.containerInfo),e===null)throw Error(c(387));l=t.pendingProps;var r=t.memoizedState;i=r.element,wc(e,t),ai(t,l,null,n);var f=t.memoizedState;if(l=f.cache,Mn(t,Fe,l),l!==r.cache&&pc(t,[Fe],n,!0),li(),l=f.element,r.isDehydrated)if(r={element:l,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=r,t.memoizedState=r,t.flags&256){t=ym(e,t,l,n);break e}else if(l!==i){i=_t(Error(c(424)),t),Pa(i),t=ym(e,t,l,n);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Xe=Vt(e.firstChild),dt=t,Oe=!0,hl=null,Wt=!0,n=em(t,null,l,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if($a(),l===i){t=yn(e,t,n);break e}at(e,t,l,n)}t=t.child}return t;case 26:return Mo(e,t),e===null?(n=Nh(t.type,null,t.pendingProps,null))?t.memoizedState=n:Oe||(n=t.type,e=t.pendingProps,l=Qo(ie.current).createElement(n),l[ct]=t,l[ht]=e,ot(l,n,e),et(l),t.stateNode=l):t.memoizedState=Nh(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Ae(t),e===null&&Oe&&(l=t.stateNode=Eh(t.type,t.pendingProps,ie.current),dt=t,Wt=!0,i=Xe,Qn(t.type)?(ju=i,Xe=Vt(l.firstChild)):Xe=i),at(e,t,t.pendingProps.children,n),Mo(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Oe&&((i=l=Xe)&&(l=rb(l,t.type,t.pendingProps,Wt),l!==null?(t.stateNode=l,dt=t,Xe=Vt(l.firstChild),Wt=!1,i=!0):i=!1),i||vl(t)),Ae(t),i=t.type,r=t.pendingProps,f=e!==null?e.memoizedProps:null,l=r.children,Du(i,r)?l=null:f!==null&&Du(i,f)&&(t.flags|=32),t.memoizedState!==null&&(i=Cc(e,t,A0,null,null,n),Ni._currentValue=i),Mo(e,t),at(e,t,l,n),t.child;case 6:return e===null&&Oe&&((e=n=Xe)&&(n=cb(n,t.pendingProps,Wt),n!==null?(t.stateNode=n,dt=t,Xe=null,e=!0):e=!1),e||vl(t)),null;case 13:return bm(e,t,n);case 4:return ce(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=na(t,null,l,n):at(e,t,l,n),t.child;case 11:return fm(e,t,t.type,t.pendingProps,n);case 7:return at(e,t,t.pendingProps,n),t.child;case 8:return at(e,t,t.pendingProps.children,n),t.child;case 12:return at(e,t,t.pendingProps.children,n),t.child;case 10:return l=t.pendingProps,Mn(t,t.type,l.value),at(e,t,l.children,n),t.child;case 9:return i=t.type._context,l=t.pendingProps.children,gl(t),i=ut(i),l=l(i),t.flags|=1,at(e,t,l,n),t.child;case 14:return dm(e,t,t.type,t.pendingProps,n);case 15:return mm(e,t,t.type,t.pendingProps,n);case 19:return Sm(e,t,n);case 31:return l=t.pendingProps,n=t.mode,l={mode:l.mode,children:l.children},e===null?(n=Oo(l,n),n.ref=t.ref,t.child=n,n.return=t,t=n):(n=fn(e.child,l),n.ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return hm(e,t,n);case 24:return gl(t),l=ut(Fe),e===null?(i=bc(),i===null&&(i=qe,r=gc(),i.pooledCache=r,r.refCount++,r!==null&&(i.pooledCacheLanes|=n),i=r),t.memoizedState={parent:l,cache:i},Sc(t),Mn(t,Fe,i)):((e.lanes&n)!==0&&(wc(e,t),ai(t,null,null,n),li()),i=e.memoizedState,r=t.memoizedState,i.parent!==l?(i={parent:l,cache:l},t.memoizedState=i,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=i),Mn(t,Fe,l)):(l=r.cache,Mn(t,Fe,l),l!==i.cache&&pc(t,[Fe],n,!0))),at(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function bn(e){e.flags|=4}function Em(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Oh(t)){if(t=Ht.current,t!==null&&((Ne&4194048)===Ne?It!==null:(Ne&62914560)!==Ne&&(Ne&536870912)===0||t!==It))throw ti=xc,od;e.flags|=8192}}function _o(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?ef():536870912,e.lanes|=t,oa|=t)}function fi(e,t){if(!Oe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Ve(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,l=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags&65011712,l|=i.flags&65011712,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,l|=i.subtreeFlags,l|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=l,e.childLanes=n,t}function z0(e,t,n){var l=t.pendingProps;switch(dc(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ve(t),null;case 1:return Ve(t),null;case 3:return n=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),vn(Fe),_e(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Ja(t)?bn(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,td())),Ve(t),null;case 26:return n=t.memoizedState,e===null?(bn(t),n!==null?(Ve(t),Em(t,n)):(Ve(t),t.flags&=-16777217)):n?n!==e.memoizedState?(bn(t),Ve(t),Em(t,n)):(Ve(t),t.flags&=-16777217):(e.memoizedProps!==l&&bn(t),Ve(t),t.flags&=-16777217),null;case 27:Ce(t),n=ie.current;var i=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&bn(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Ve(t),null}e=W.current,Ja(t)?If(t):(e=Eh(i,l,n),t.stateNode=e,bn(t))}return Ve(t),null;case 5:if(Ce(t),n=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&bn(t);else{if(!l){if(t.stateNode===null)throw Error(c(166));return Ve(t),null}if(e=W.current,Ja(t))If(t);else{switch(i=Qo(ie.current),e){case 1:e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=i.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=i.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?i.createElement("select",{is:l.is}):i.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?i.createElement(n,{is:l.is}):i.createElement(n)}}e[ct]=t,e[ht]=l;e:for(i=t.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.tag!==27&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===t)break e;for(;i.sibling===null;){if(i.return===null||i.return===t)break e;i=i.return}i.sibling.return=i.return,i=i.sibling}t.stateNode=e;e:switch(ot(e,n,l),n){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&bn(t)}}return Ve(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&bn(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(c(166));if(e=ie.current,Ja(t)){if(e=t.stateNode,n=t.memoizedProps,l=null,i=dt,i!==null)switch(i.tag){case 27:case 5:l=i.memoizedProps}e[ct]=t,e=!!(e.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||ph(e.nodeValue,n)),e||vl(t)}else e=Qo(e).createTextNode(l),e[ct]=t,t.stateNode=e}return Ve(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(i=Ja(t),l!==null&&l.dehydrated!==null){if(e===null){if(!i)throw Error(c(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(c(317));i[ct]=t}else $a(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ve(t),i=!1}else i=td(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=i),i=!0;if(!i)return t.flags&256?(gn(t),t):(gn(t),null)}if(gn(t),(t.flags&128)!==0)return t.lanes=n,t;if(n=l!==null,e=e!==null&&e.memoizedState!==null,n){l=t.child,i=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(i=l.alternate.memoizedState.cachePool.pool);var r=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(r=l.memoizedState.cachePool.pool),r!==i&&(l.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),_o(t,t.updateQueue),Ve(t),null;case 4:return _e(),e===null&&Tu(t.stateNode.containerInfo),Ve(t),null;case 10:return vn(t.type),Ve(t),null;case 19:if(F(We),i=t.memoizedState,i===null)return Ve(t),null;if(l=(t.flags&128)!==0,r=i.rendering,r===null)if(l)fi(i,!1);else{if(Qe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(r=Ro(e),r!==null){for(t.flags|=128,fi(i,!1),e=r.updateQueue,t.updateQueue=e,_o(t,e),t.subtreeFlags=0,e=n,n=t.child;n!==null;)Ff(n,e),n=n.sibling;return J(We,We.current&1|2),t.child}e=e.sibling}i.tail!==null&&mt()>Uo&&(t.flags|=128,l=!0,fi(i,!1),t.lanes=4194304)}else{if(!l)if(e=Ro(r),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,_o(t,e),fi(i,!0),i.tail===null&&i.tailMode==="hidden"&&!r.alternate&&!Oe)return Ve(t),null}else 2*mt()-i.renderingStartTime>Uo&&n!==536870912&&(t.flags|=128,l=!0,fi(i,!1),t.lanes=4194304);i.isBackwards?(r.sibling=t.child,t.child=r):(e=i.last,e!==null?e.sibling=r:t.child=r,i.last=r)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=mt(),t.sibling=null,e=We.current,J(We,l?e&1|2:e&1),t):(Ve(t),null);case 22:case 23:return gn(t),Nc(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(n&536870912)!==0&&(t.flags&128)===0&&(Ve(t),t.subtreeFlags&6&&(t.flags|=8192)):Ve(t),n=t.updateQueue,n!==null&&_o(t,n.retryQueue),n=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==n&&(t.flags|=2048),e!==null&&F(yl),null;case 24:return n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),vn(Fe),Ve(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function U0(e,t){switch(dc(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return vn(Fe),_e(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Ce(t),null;case 13:if(gn(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));$a()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return F(We),null;case 4:return _e(),null;case 10:return vn(t.type),null;case 22:case 23:return gn(t),Nc(),e!==null&&F(yl),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return vn(Fe),null;case 25:return null;default:return null}}function Tm(e,t){switch(dc(t),t.tag){case 3:vn(Fe),_e();break;case 26:case 27:case 5:Ce(t);break;case 4:_e();break;case 13:gn(t);break;case 19:F(We);break;case 10:vn(t.type);break;case 22:case 23:gn(t),Nc(),e!==null&&F(yl);break;case 24:vn(Fe)}}function di(e,t){try{var n=t.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&e)===e){l=void 0;var r=n.create,f=n.inst;l=r(),f.destroy=l}n=n.next}while(n!==i)}}catch(p){ke(t,t.return,p)}}function Bn(e,t,n){try{var l=t.updateQueue,i=l!==null?l.lastEffect:null;if(i!==null){var r=i.next;l=r;do{if((l.tag&e)===e){var f=l.inst,p=f.destroy;if(p!==void 0){f.destroy=void 0,i=t;var S=n,j=p;try{j()}catch(q){ke(i,S,q)}}}l=l.next}while(l!==r)}}catch(q){ke(t,t.return,q)}}function Am(e){var t=e.updateQueue;if(t!==null){var n=e.stateNode;try{dd(t,n)}catch(l){ke(e,e.return,l)}}}function Nm(e,t,n){n.props=xl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(l){ke(e,t,l)}}function mi(e,t){try{var n=e.ref;if(n!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof n=="function"?e.refCleanup=n(l):n.current=l}}catch(i){ke(e,t,i)}}function en(e,t){var n=e.ref,l=e.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(i){ke(e,t,i)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(i){ke(e,t,i)}else n.current=null}function Rm(e){var t=e.type,n=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break e;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(i){ke(e,e.return,i)}}function eu(e,t,n){try{var l=e.stateNode;nb(l,e.type,n,t),l[ht]=t}catch(i){ke(e,e.return,i)}}function Cm(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Qn(e.type)||e.tag===4}function tu(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Cm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Qn(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function nu(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(e,t):(t=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,t.appendChild(e),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Xo));else if(l!==4&&(l===27&&Qn(e.type)&&(n=e.stateNode,t=null),e=e.child,e!==null))for(nu(e,t,n),e=e.sibling;e!==null;)nu(e,t,n),e=e.sibling}function jo(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(l!==4&&(l===27&&Qn(e.type)&&(n=e.stateNode),e=e.child,e!==null))for(jo(e,t,n),e=e.sibling;e!==null;)jo(e,t,n),e=e.sibling}function Dm(e){var t=e.stateNode,n=e.memoizedProps;try{for(var l=e.type,i=t.attributes;i.length;)t.removeAttributeNode(i[0]);ot(t,l,n),t[ct]=e,t[ht]=n}catch(r){ke(e,e.return,r)}}var xn=!1,Je=!1,lu=!1,Mm=typeof WeakSet=="function"?WeakSet:Set,nt=null;function H0(e,t){if(e=e.containerInfo,Ru=Fo,e=Gf(e),tc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var i=l.anchorOffset,r=l.focusNode;l=l.focusOffset;try{n.nodeType,r.nodeType}catch{n=null;break e}var f=0,p=-1,S=-1,j=0,q=0,X=e,U=null;t:for(;;){for(var H;X!==n||i!==0&&X.nodeType!==3||(p=f+i),X!==r||l!==0&&X.nodeType!==3||(S=f+l),X.nodeType===3&&(f+=X.nodeValue.length),(H=X.firstChild)!==null;)U=X,X=H;for(;;){if(X===e)break t;if(U===n&&++j===i&&(p=f),U===r&&++q===l&&(S=f),(H=X.nextSibling)!==null)break;X=U,U=X.parentNode}X=H}n=p===-1||S===-1?null:{start:p,end:S}}else n=null}n=n||{start:0,end:0}}else n=null;for(Cu={focusedElem:e,selectionRange:n},Fo=!1,nt=t;nt!==null;)if(t=nt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,nt=e;else for(;nt!==null;){switch(t=nt,r=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&r!==null){e=void 0,n=t,i=r.memoizedProps,r=r.memoizedState,l=n.stateNode;try{var de=xl(n.type,i,n.elementType===n.type);e=l.getSnapshotBeforeUpdate(de,r),l.__reactInternalSnapshotBeforeUpdate=e}catch(re){ke(n,n.return,re)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,n=e.nodeType,n===9)Ou(e);else if(n===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Ou(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,nt=e;break}nt=t.return}}function Om(e,t,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:Ln(e,n),l&4&&di(5,n);break;case 1:if(Ln(e,n),l&4)if(e=n.stateNode,t===null)try{e.componentDidMount()}catch(f){ke(n,n.return,f)}else{var i=xl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(i,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){ke(n,n.return,f)}}l&64&&Am(n),l&512&&mi(n,n.return);break;case 3:if(Ln(e,n),l&64&&(e=n.updateQueue,e!==null)){if(t=null,n.child!==null)switch(n.child.tag){case 27:case 5:t=n.child.stateNode;break;case 1:t=n.child.stateNode}try{dd(e,t)}catch(f){ke(n,n.return,f)}}break;case 27:t===null&&l&4&&Dm(n);case 26:case 5:Ln(e,n),t===null&&l&4&&Rm(n),l&512&&mi(n,n.return);break;case 12:Ln(e,n);break;case 13:Ln(e,n),l&4&&zm(e,n),l&64&&(e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(n=Q0.bind(null,n),ub(e,n))));break;case 22:if(l=n.memoizedState!==null||xn,!l){t=t!==null&&t.memoizedState!==null||Je,i=xn;var r=Je;xn=l,(Je=t)&&!r?kn(e,n,(n.subtreeFlags&8772)!==0):Ln(e,n),xn=i,Je=r}break;case 30:break;default:Ln(e,n)}}function _m(e){var t=e.alternate;t!==null&&(e.alternate=null,_m(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&Hr(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ye=null,gt=!1;function Sn(e,t,n){for(n=n.child;n!==null;)jm(e,t,n),n=n.sibling}function jm(e,t,n){if(bt&&typeof bt.onCommitFiberUnmount=="function")try{bt.onCommitFiberUnmount(za,n)}catch{}switch(n.tag){case 26:Je||en(n,t),Sn(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Je||en(n,t);var l=Ye,i=gt;Qn(n.type)&&(Ye=n.stateNode,gt=!1),Sn(e,t,n),wi(n.stateNode),Ye=l,gt=i;break;case 5:Je||en(n,t);case 6:if(l=Ye,i=gt,Ye=null,Sn(e,t,n),Ye=l,gt=i,Ye!==null)if(gt)try{(Ye.nodeType===9?Ye.body:Ye.nodeName==="HTML"?Ye.ownerDocument.body:Ye).removeChild(n.stateNode)}catch(r){ke(n,t,r)}else try{Ye.removeChild(n.stateNode)}catch(r){ke(n,t,r)}break;case 18:Ye!==null&&(gt?(e=Ye,Sh(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,n.stateNode),Mi(e)):Sh(Ye,n.stateNode));break;case 4:l=Ye,i=gt,Ye=n.stateNode.containerInfo,gt=!0,Sn(e,t,n),Ye=l,gt=i;break;case 0:case 11:case 14:case 15:Je||Bn(2,n,t),Je||Bn(4,n,t),Sn(e,t,n);break;case 1:Je||(en(n,t),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Nm(n,t,l)),Sn(e,t,n);break;case 21:Sn(e,t,n);break;case 22:Je=(l=Je)||n.memoizedState!==null,Sn(e,t,n),Je=l;break;default:Sn(e,t,n)}}function zm(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Mi(e)}catch(n){ke(t,t.return,n)}}function B0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Mm),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Mm),t;default:throw Error(c(435,e.tag))}}function au(e,t){var n=B0(e);t.forEach(function(l){var i=Z0.bind(null,e,l);n.has(l)||(n.add(l),l.then(i,i))})}function Et(e,t){var n=t.deletions;if(n!==null)for(var l=0;l<n.length;l++){var i=n[l],r=e,f=t,p=f;e:for(;p!==null;){switch(p.tag){case 27:if(Qn(p.type)){Ye=p.stateNode,gt=!1;break e}break;case 5:Ye=p.stateNode,gt=!1;break e;case 3:case 4:Ye=p.stateNode.containerInfo,gt=!0;break e}p=p.return}if(Ye===null)throw Error(c(160));jm(r,f,i),Ye=null,gt=!1,r=i.alternate,r!==null&&(r.return=null),i.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Um(t,e),t=t.sibling}var Yt=null;function Um(e,t){var n=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Et(t,e),Tt(e),l&4&&(Bn(3,e,e.return),di(3,e),Bn(5,e,e.return));break;case 1:Et(t,e),Tt(e),l&512&&(Je||n===null||en(n,n.return)),l&64&&xn&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var i=Yt;if(Et(t,e),Tt(e),l&512&&(Je||n===null||en(n,n.return)),l&4){var r=n!==null?n.memoizedState:null;if(l=e.memoizedState,n===null)if(l===null)if(e.stateNode===null){e:{l=e.type,n=e.memoizedProps,i=i.ownerDocument||i;t:switch(l){case"title":r=i.getElementsByTagName("title")[0],(!r||r[Ba]||r[ct]||r.namespaceURI==="http://www.w3.org/2000/svg"||r.hasAttribute("itemprop"))&&(r=i.createElement(l),i.head.insertBefore(r,i.querySelector("head > title"))),ot(r,l,n),r[ct]=e,et(r),l=r;break e;case"link":var f=Dh("link","href",i).get(l+(n.href||""));if(f){for(var p=0;p<f.length;p++)if(r=f[p],r.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&r.getAttribute("rel")===(n.rel==null?null:n.rel)&&r.getAttribute("title")===(n.title==null?null:n.title)&&r.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){f.splice(p,1);break t}}r=i.createElement(l),ot(r,l,n),i.head.appendChild(r);break;case"meta":if(f=Dh("meta","content",i).get(l+(n.content||""))){for(p=0;p<f.length;p++)if(r=f[p],r.getAttribute("content")===(n.content==null?null:""+n.content)&&r.getAttribute("name")===(n.name==null?null:n.name)&&r.getAttribute("property")===(n.property==null?null:n.property)&&r.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&r.getAttribute("charset")===(n.charSet==null?null:n.charSet)){f.splice(p,1);break t}}r=i.createElement(l),ot(r,l,n),i.head.appendChild(r);break;default:throw Error(c(468,l))}r[ct]=e,et(r),l=r}e.stateNode=l}else Mh(i,e.type,e.stateNode);else e.stateNode=Ch(i,l,e.memoizedProps);else r!==l?(r===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):r.count--,l===null?Mh(i,e.type,e.stateNode):Ch(i,l,e.memoizedProps)):l===null&&e.stateNode!==null&&eu(e,e.memoizedProps,n.memoizedProps)}break;case 27:Et(t,e),Tt(e),l&512&&(Je||n===null||en(n,n.return)),n!==null&&l&4&&eu(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Et(t,e),Tt(e),l&512&&(Je||n===null||en(n,n.return)),e.flags&32){i=e.stateNode;try{Ll(i,"")}catch(H){ke(e,e.return,H)}}l&4&&e.stateNode!=null&&(i=e.memoizedProps,eu(e,i,n!==null?n.memoizedProps:i)),l&1024&&(lu=!0);break;case 6:if(Et(t,e),Tt(e),l&4){if(e.stateNode===null)throw Error(c(162));l=e.memoizedProps,n=e.stateNode;try{n.nodeValue=l}catch(H){ke(e,e.return,H)}}break;case 3:if(Jo=null,i=Yt,Yt=Zo(t.containerInfo),Et(t,e),Yt=i,Tt(e),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Mi(t.containerInfo)}catch(H){ke(e,e.return,H)}lu&&(lu=!1,Hm(e));break;case 4:l=Yt,Yt=Zo(e.stateNode.containerInfo),Et(t,e),Tt(e),Yt=l;break;case 12:Et(t,e),Tt(e);break;case 13:Et(t,e),Tt(e),e.child.flags&8192&&e.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(su=mt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,au(e,l)));break;case 22:i=e.memoizedState!==null;var S=n!==null&&n.memoizedState!==null,j=xn,q=Je;if(xn=j||i,Je=q||S,Et(t,e),Je=q,xn=j,Tt(e),l&8192)e:for(t=e.stateNode,t._visibility=i?t._visibility&-2:t._visibility|1,i&&(n===null||S||xn||Je||Sl(e)),n=null,t=e;;){if(t.tag===5||t.tag===26){if(n===null){S=n=t;try{if(r=S.stateNode,i)f=r.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{p=S.stateNode;var X=S.memoizedProps.style,U=X!=null&&X.hasOwnProperty("display")?X.display:null;p.style.display=U==null||typeof U=="boolean"?"":(""+U).trim()}}catch(H){ke(S,S.return,H)}}}else if(t.tag===6){if(n===null){S=t;try{S.stateNode.nodeValue=i?"":S.memoizedProps}catch(H){ke(S,S.return,H)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,au(e,n))));break;case 19:Et(t,e),Tt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,au(e,l)));break;case 30:break;case 21:break;default:Et(t,e),Tt(e)}}function Tt(e){var t=e.flags;if(t&2){try{for(var n,l=e.return;l!==null;){if(Cm(l)){n=l;break}l=l.return}if(n==null)throw Error(c(160));switch(n.tag){case 27:var i=n.stateNode,r=tu(e);jo(e,r,i);break;case 5:var f=n.stateNode;n.flags&32&&(Ll(f,""),n.flags&=-33);var p=tu(e);jo(e,p,f);break;case 3:case 4:var S=n.stateNode.containerInfo,j=tu(e);nu(e,j,S);break;default:throw Error(c(161))}}catch(q){ke(e,e.return,q)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Hm(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Hm(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Ln(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Om(e,t.alternate,t),t=t.sibling}function Sl(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Bn(4,t,t.return),Sl(t);break;case 1:en(t,t.return);var n=t.stateNode;typeof n.componentWillUnmount=="function"&&Nm(t,t.return,n),Sl(t);break;case 27:wi(t.stateNode);case 26:case 5:en(t,t.return),Sl(t);break;case 22:t.memoizedState===null&&Sl(t);break;case 30:Sl(t);break;default:Sl(t)}e=e.sibling}}function kn(e,t,n){for(n=n&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,i=e,r=t,f=r.flags;switch(r.tag){case 0:case 11:case 15:kn(i,r,n),di(4,r);break;case 1:if(kn(i,r,n),l=r,i=l.stateNode,typeof i.componentDidMount=="function")try{i.componentDidMount()}catch(j){ke(l,l.return,j)}if(l=r,i=l.updateQueue,i!==null){var p=l.stateNode;try{var S=i.shared.hiddenCallbacks;if(S!==null)for(i.shared.hiddenCallbacks=null,i=0;i<S.length;i++)fd(S[i],p)}catch(j){ke(l,l.return,j)}}n&&f&64&&Am(r),mi(r,r.return);break;case 27:Dm(r);case 26:case 5:kn(i,r,n),n&&l===null&&f&4&&Rm(r),mi(r,r.return);break;case 12:kn(i,r,n);break;case 13:kn(i,r,n),n&&f&4&&zm(i,r);break;case 22:r.memoizedState===null&&kn(i,r,n),mi(r,r.return);break;case 30:break;default:kn(i,r,n)}t=t.sibling}}function iu(e,t){var n=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==n&&(e!=null&&e.refCount++,n!=null&&Wa(n))}function ou(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Wa(e))}function tn(e,t,n,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Bm(e,t,n,l),t=t.sibling}function Bm(e,t,n,l){var i=t.flags;switch(t.tag){case 0:case 11:case 15:tn(e,t,n,l),i&2048&&di(9,t);break;case 1:tn(e,t,n,l);break;case 3:tn(e,t,n,l),i&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Wa(e)));break;case 12:if(i&2048){tn(e,t,n,l),e=t.stateNode;try{var r=t.memoizedProps,f=r.id,p=r.onPostCommit;typeof p=="function"&&p(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(S){ke(t,t.return,S)}}else tn(e,t,n,l);break;case 13:tn(e,t,n,l);break;case 23:break;case 22:r=t.stateNode,f=t.alternate,t.memoizedState!==null?r._visibility&2?tn(e,t,n,l):hi(e,t):r._visibility&2?tn(e,t,n,l):(r._visibility|=2,la(e,t,n,l,(t.subtreeFlags&10256)!==0)),i&2048&&iu(f,t);break;case 24:tn(e,t,n,l),i&2048&&ou(t.alternate,t);break;default:tn(e,t,n,l)}}function la(e,t,n,l,i){for(i=i&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var r=e,f=t,p=n,S=l,j=f.flags;switch(f.tag){case 0:case 11:case 15:la(r,f,p,S,i),di(8,f);break;case 23:break;case 22:var q=f.stateNode;f.memoizedState!==null?q._visibility&2?la(r,f,p,S,i):hi(r,f):(q._visibility|=2,la(r,f,p,S,i)),i&&j&2048&&iu(f.alternate,f);break;case 24:la(r,f,p,S,i),i&&j&2048&&ou(f.alternate,f);break;default:la(r,f,p,S,i)}t=t.sibling}}function hi(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var n=e,l=t,i=l.flags;switch(l.tag){case 22:hi(n,l),i&2048&&iu(l.alternate,l);break;case 24:hi(n,l),i&2048&&ou(l.alternate,l);break;default:hi(n,l)}t=t.sibling}}var vi=8192;function aa(e){if(e.subtreeFlags&vi)for(e=e.child;e!==null;)Lm(e),e=e.sibling}function Lm(e){switch(e.tag){case 26:aa(e),e.flags&vi&&e.memoizedState!==null&&wb(Yt,e.memoizedState,e.memoizedProps);break;case 5:aa(e);break;case 3:case 4:var t=Yt;Yt=Zo(e.stateNode.containerInfo),aa(e),Yt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=vi,vi=16777216,aa(e),vi=t):aa(e));break;default:aa(e)}}function km(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function pi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];nt=l,Gm(l,e)}km(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)qm(e),e=e.sibling}function qm(e){switch(e.tag){case 0:case 11:case 15:pi(e),e.flags&2048&&Bn(9,e,e.return);break;case 3:pi(e);break;case 12:pi(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,zo(e)):pi(e);break;default:pi(e)}}function zo(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var n=0;n<t.length;n++){var l=t[n];nt=l,Gm(l,e)}km(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Bn(8,t,t.return),zo(t);break;case 22:n=t.stateNode,n._visibility&2&&(n._visibility&=-3,zo(t));break;default:zo(t)}e=e.sibling}}function Gm(e,t){for(;nt!==null;){var n=nt;switch(n.tag){case 0:case 11:case 15:Bn(8,n,t);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Wa(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,nt=l;else e:for(n=e;nt!==null;){l=nt;var i=l.sibling,r=l.return;if(_m(l),l===n){nt=null;break e}if(i!==null){i.return=r,nt=i;break e}nt=r}}}var L0={getCacheForType:function(e){var t=ut(Fe),n=t.data.get(e);return n===void 0&&(n=e(),t.data.set(e,n)),n}},k0=typeof WeakMap=="function"?WeakMap:Map,ze=0,qe=null,we=null,Ne=0,Ue=0,At=null,qn=!1,ia=!1,ru=!1,wn=0,Qe=0,Gn=0,wl=0,cu=0,Bt=0,oa=0,gi=null,yt=null,uu=!1,su=0,Uo=1/0,Ho=null,Yn=null,it=0,Vn=null,ra=null,ca=0,fu=0,du=null,Ym=null,yi=0,mu=null;function Nt(){if((ze&2)!==0&&Ne!==0)return Ne&-Ne;if(M.T!==null){var e=$l;return e!==0?e:xu()}return lf()}function Vm(){Bt===0&&(Bt=(Ne&536870912)===0||Oe?Is():536870912);var e=Ht.current;return e!==null&&(e.flags|=32),Bt}function Rt(e,t,n){(e===qe&&(Ue===2||Ue===9)||e.cancelPendingCommit!==null)&&(ua(e,0),Xn(e,Ne,Bt,!1)),Ha(e,n),((ze&2)===0||e!==qe)&&(e===qe&&((ze&2)===0&&(wl|=n),Qe===4&&Xn(e,Ne,Bt,!1)),nn(e))}function Xm(e,t,n){if((ze&6)!==0)throw Error(c(327));var l=!n&&(t&124)===0&&(t&e.expiredLanes)===0||Ua(e,t),i=l?Y0(e,t):pu(e,t,!0),r=l;do{if(i===0){ia&&!l&&Xn(e,t,0,!1);break}else{if(n=e.current.alternate,r&&!q0(n)){i=pu(e,t,!1),r=!1;continue}if(i===2){if(r=t,e.errorRecoveryDisabledLanes&r)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var p=e;i=gi;var S=p.current.memoizedState.isDehydrated;if(S&&(ua(p,f).flags|=256),f=pu(p,f,!1),f!==2){if(ru&&!S){p.errorRecoveryDisabledLanes|=r,wl|=r,i=4;break e}r=yt,yt=i,r!==null&&(yt===null?yt=r:yt.push.apply(yt,r))}i=f}if(r=!1,i!==2)continue}}if(i===1){ua(e,0),Xn(e,t,0,!0);break}e:{switch(l=e,r=i,r){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:Xn(l,t,Bt,!qn);break e;case 2:yt=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(i=su+300-mt(),10<i)){if(Xn(l,t,Bt,!qn),Zi(l,0,!0)!==0)break e;l.timeoutHandle=bh(Qm.bind(null,l,n,yt,Ho,uu,t,Bt,wl,oa,qn,r,2,-0,0),i);break e}Qm(l,n,yt,Ho,uu,t,Bt,wl,oa,qn,r,0,-0,0)}}break}while(!0);nn(e)}function Qm(e,t,n,l,i,r,f,p,S,j,q,X,U,H){if(e.timeoutHandle=-1,X=t.subtreeFlags,(X&8192||(X&16785408)===16785408)&&(Ai={stylesheets:null,count:0,unsuspend:Sb},Lm(t),X=Eb(),X!==null)){e.cancelPendingCommit=X(Wm.bind(null,e,t,r,n,l,i,f,p,S,q,1,U,H)),Xn(e,r,f,!j);return}Wm(e,t,r,n,l,i,f,p,S)}function q0(e){for(var t=e;;){var n=t.tag;if((n===0||n===11||n===15)&&t.flags&16384&&(n=t.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var i=n[l],r=i.getSnapshot;i=i.value;try{if(!St(r(),i))return!1}catch{return!1}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Xn(e,t,n,l){t&=~cu,t&=~wl,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var i=t;0<i;){var r=31-xt(i),f=1<<r;l[r]=-1,i&=~f}n!==0&&tf(e,n,t)}function Bo(){return(ze&6)===0?(bi(0),!1):!0}function hu(){if(we!==null){if(Ue===0)var e=we.return;else e=we,hn=pl=null,Oc(e),ta=null,ui=0,e=we;for(;e!==null;)Tm(e.alternate,e),e=e.return;we=null}}function ua(e,t){var n=e.timeoutHandle;n!==-1&&(e.timeoutHandle=-1,ab(n)),n=e.cancelPendingCommit,n!==null&&(e.cancelPendingCommit=null,n()),hu(),qe=e,we=n=fn(e.current,null),Ne=t,Ue=0,At=null,qn=!1,ia=Ua(e,t),ru=!1,oa=Bt=cu=wl=Gn=Qe=0,yt=gi=null,uu=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var i=31-xt(l),r=1<<i;t|=e[i],l&=~r}return wn=t,ao(),n}function Zm(e,t){be=null,M.H=To,t===ei||t===ho?(t=ud(),Ue=3):t===od?(t=ud(),Ue=4):Ue=t===sm?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,At=t,we===null&&(Qe=1,Do(e,_t(t,e.current)))}function Km(){var e=M.H;return M.H=To,e===null?To:e}function Jm(){var e=M.A;return M.A=L0,e}function vu(){Qe=4,qn||(Ne&4194048)!==Ne&&Ht.current!==null||(ia=!0),(Gn&134217727)===0&&(wl&134217727)===0||qe===null||Xn(qe,Ne,Bt,!1)}function pu(e,t,n){var l=ze;ze|=2;var i=Km(),r=Jm();(qe!==e||Ne!==t)&&(Ho=null,ua(e,t)),t=!1;var f=Qe;e:do try{if(Ue!==0&&we!==null){var p=we,S=At;switch(Ue){case 8:hu(),f=6;break e;case 3:case 2:case 9:case 6:Ht.current===null&&(t=!0);var j=Ue;if(Ue=0,At=null,sa(e,p,S,j),n&&ia){f=0;break e}break;default:j=Ue,Ue=0,At=null,sa(e,p,S,j)}}G0(),f=Qe;break}catch(q){Zm(e,q)}while(!0);return t&&e.shellSuspendCounter++,hn=pl=null,ze=l,M.H=i,M.A=r,we===null&&(qe=null,Ne=0,ao()),f}function G0(){for(;we!==null;)$m(we)}function Y0(e,t){var n=ze;ze|=2;var l=Km(),i=Jm();qe!==e||Ne!==t?(Ho=null,Uo=mt()+500,ua(e,t)):ia=Ua(e,t);e:do try{if(Ue!==0&&we!==null){t=we;var r=At;t:switch(Ue){case 1:Ue=0,At=null,sa(e,t,r,1);break;case 2:case 9:if(rd(r)){Ue=0,At=null,Pm(t);break}t=function(){Ue!==2&&Ue!==9||qe!==e||(Ue=7),nn(e)},r.then(t,t);break e;case 3:Ue=7;break e;case 4:Ue=5;break e;case 7:rd(r)?(Ue=0,At=null,Pm(t)):(Ue=0,At=null,sa(e,t,r,7));break;case 5:var f=null;switch(we.tag){case 26:f=we.memoizedState;case 5:case 27:var p=we;if(!f||Oh(f)){Ue=0,At=null;var S=p.sibling;if(S!==null)we=S;else{var j=p.return;j!==null?(we=j,Lo(j)):we=null}break t}}Ue=0,At=null,sa(e,t,r,5);break;case 6:Ue=0,At=null,sa(e,t,r,6);break;case 8:hu(),Qe=6;break e;default:throw Error(c(462))}}V0();break}catch(q){Zm(e,q)}while(!0);return hn=pl=null,M.H=l,M.A=i,ze=n,we!==null?0:(qe=null,Ne=0,ao(),Qe)}function V0(){for(;we!==null&&!_a();)$m(we)}function $m(e){var t=wm(e.alternate,e,wn);e.memoizedProps=e.pendingProps,t===null?Lo(e):we=t}function Pm(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=pm(n,t,t.pendingProps,t.type,void 0,Ne);break;case 11:t=pm(n,t,t.pendingProps,t.type.render,t.ref,Ne);break;case 5:Oc(t);default:Tm(n,t),t=we=Ff(t,wn),t=wm(n,t,wn)}e.memoizedProps=e.pendingProps,t===null?Lo(e):we=t}function sa(e,t,n,l){hn=pl=null,Oc(t),ta=null,ui=0;var i=t.return;try{if(_0(e,i,t,n,Ne)){Qe=1,Do(e,_t(n,e.current)),we=null;return}}catch(r){if(i!==null)throw we=i,r;Qe=1,Do(e,_t(n,e.current)),we=null;return}t.flags&32768?(Oe||l===1?e=!0:ia||(Ne&536870912)!==0?e=!1:(qn=e=!0,(l===2||l===9||l===3||l===6)&&(l=Ht.current,l!==null&&l.tag===13&&(l.flags|=16384))),Fm(t,e)):Lo(t)}function Lo(e){var t=e;do{if((t.flags&32768)!==0){Fm(t,qn);return}e=t.return;var n=z0(t.alternate,t,wn);if(n!==null){we=n;return}if(t=t.sibling,t!==null){we=t;return}we=t=e}while(t!==null);Qe===0&&(Qe=5)}function Fm(e,t){do{var n=U0(e.alternate,e);if(n!==null){n.flags&=32767,we=n;return}if(n=e.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&(e=e.sibling,e!==null)){we=e;return}we=e=n}while(e!==null);Qe=6,we=null}function Wm(e,t,n,l,i,r,f,p,S){e.cancelPendingCommit=null;do ko();while(it!==0);if((ze&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(r=t.lanes|t.childLanes,r|=oc,Sy(e,n,r,f,p,S),e===qe&&(we=qe=null,Ne=0),ra=t,Vn=e,ca=n,fu=r,du=i,Ym=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,K0(Vi,function(){return lh(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=M.T,M.T=null,i=Q.p,Q.p=2,f=ze,ze|=4;try{H0(e,t,n)}finally{ze=f,Q.p=i,M.T=l}}it=1,Im(),eh(),th()}}function Im(){if(it===1){it=0;var e=Vn,t=ra,n=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||n){n=M.T,M.T=null;var l=Q.p;Q.p=2;var i=ze;ze|=4;try{Um(t,e);var r=Cu,f=Gf(e.containerInfo),p=r.focusedElem,S=r.selectionRange;if(f!==p&&p&&p.ownerDocument&&qf(p.ownerDocument.documentElement,p)){if(S!==null&&tc(p)){var j=S.start,q=S.end;if(q===void 0&&(q=j),"selectionStart"in p)p.selectionStart=j,p.selectionEnd=Math.min(q,p.value.length);else{var X=p.ownerDocument||document,U=X&&X.defaultView||window;if(U.getSelection){var H=U.getSelection(),de=p.textContent.length,re=Math.min(S.start,de),Le=S.end===void 0?re:Math.min(S.end,de);!H.extend&&re>Le&&(f=Le,Le=re,re=f);var C=kf(p,re),A=kf(p,Le);if(C&&A&&(H.rangeCount!==1||H.anchorNode!==C.node||H.anchorOffset!==C.offset||H.focusNode!==A.node||H.focusOffset!==A.offset)){var O=X.createRange();O.setStart(C.node,C.offset),H.removeAllRanges(),re>Le?(H.addRange(O),H.extend(A.node,A.offset)):(O.setEnd(A.node,A.offset),H.addRange(O))}}}}for(X=[],H=p;H=H.parentNode;)H.nodeType===1&&X.push({element:H,left:H.scrollLeft,top:H.scrollTop});for(typeof p.focus=="function"&&p.focus(),p=0;p<X.length;p++){var Y=X[p];Y.element.scrollLeft=Y.left,Y.element.scrollTop=Y.top}}Fo=!!Ru,Cu=Ru=null}finally{ze=i,Q.p=l,M.T=n}}e.current=t,it=2}}function eh(){if(it===2){it=0;var e=Vn,t=ra,n=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||n){n=M.T,M.T=null;var l=Q.p;Q.p=2;var i=ze;ze|=4;try{Om(e,t.alternate,t)}finally{ze=i,Q.p=l,M.T=n}}it=3}}function th(){if(it===4||it===3){it=0,ja();var e=Vn,t=ra,n=ca,l=Ym;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?it=5:(it=0,ra=Vn=null,nh(e,e.pendingLanes));var i=e.pendingLanes;if(i===0&&(Yn=null),zr(n),t=t.stateNode,bt&&typeof bt.onCommitFiberRoot=="function")try{bt.onCommitFiberRoot(za,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=M.T,i=Q.p,Q.p=2,M.T=null;try{for(var r=e.onRecoverableError,f=0;f<l.length;f++){var p=l[f];r(p.value,{componentStack:p.stack})}}finally{M.T=t,Q.p=i}}(ca&3)!==0&&ko(),nn(e),i=e.pendingLanes,(n&4194090)!==0&&(i&42)!==0?e===mu?yi++:(yi=0,mu=e):yi=0,bi(0)}}function nh(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Wa(t)))}function ko(e){return Im(),eh(),th(),lh()}function lh(){if(it!==5)return!1;var e=Vn,t=fu;fu=0;var n=zr(ca),l=M.T,i=Q.p;try{Q.p=32>n?32:n,M.T=null,n=du,du=null;var r=Vn,f=ca;if(it=0,ra=Vn=null,ca=0,(ze&6)!==0)throw Error(c(331));var p=ze;if(ze|=4,qm(r.current),Bm(r,r.current,f,n),ze=p,bi(0,!1),bt&&typeof bt.onPostCommitFiberRoot=="function")try{bt.onPostCommitFiberRoot(za,r)}catch{}return!0}finally{Q.p=i,M.T=l,nh(e,t)}}function ah(e,t,n){t=_t(n,t),t=Qc(e.stateNode,t,2),e=jn(e,t,2),e!==null&&(Ha(e,2),nn(e))}function ke(e,t,n){if(e.tag===3)ah(e,e,n);else for(;t!==null;){if(t.tag===3){ah(t,e,n);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Yn===null||!Yn.has(l))){e=_t(n,e),n=cm(2),l=jn(t,n,2),l!==null&&(um(n,l,t,e),Ha(l,2),nn(l));break}}t=t.return}}function gu(e,t,n){var l=e.pingCache;if(l===null){l=e.pingCache=new k0;var i=new Set;l.set(t,i)}else i=l.get(t),i===void 0&&(i=new Set,l.set(t,i));i.has(n)||(ru=!0,i.add(n),e=X0.bind(null,e,t,n),t.then(e,e))}function X0(e,t,n){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,qe===e&&(Ne&n)===n&&(Qe===4||Qe===3&&(Ne&62914560)===Ne&&300>mt()-su?(ze&2)===0&&ua(e,0):cu|=n,oa===Ne&&(oa=0)),nn(e)}function ih(e,t){t===0&&(t=ef()),e=Ql(e,t),e!==null&&(Ha(e,t),nn(e))}function Q0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),ih(e,n)}function Z0(e,t){var n=0;switch(e.tag){case 13:var l=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(c(314))}l!==null&&l.delete(t),ih(e,n)}function K0(e,t){return rt(e,t)}var qo=null,fa=null,yu=!1,Go=!1,bu=!1,El=0;function nn(e){e!==fa&&e.next===null&&(fa===null?qo=fa=e:fa=fa.next=e),Go=!0,yu||(yu=!0,$0())}function bi(e,t){if(!bu&&Go){bu=!0;do for(var n=!1,l=qo;l!==null;){if(e!==0){var i=l.pendingLanes;if(i===0)var r=0;else{var f=l.suspendedLanes,p=l.pingedLanes;r=(1<<31-xt(42|e)+1)-1,r&=i&~(f&~p),r=r&201326741?r&201326741|1:r?r|2:0}r!==0&&(n=!0,uh(l,r))}else r=Ne,r=Zi(l,l===qe?r:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(r&3)===0||Ua(l,r)||(n=!0,uh(l,r));l=l.next}while(n);bu=!1}}function J0(){oh()}function oh(){Go=yu=!1;var e=0;El!==0&&(lb()&&(e=El),El=0);for(var t=mt(),n=null,l=qo;l!==null;){var i=l.next,r=rh(l,t);r===0?(l.next=null,n===null?qo=i:n.next=i,i===null&&(fa=n)):(n=l,(e!==0||(r&3)!==0)&&(Go=!0)),l=i}bi(e)}function rh(e,t){for(var n=e.suspendedLanes,l=e.pingedLanes,i=e.expirationTimes,r=e.pendingLanes&-62914561;0<r;){var f=31-xt(r),p=1<<f,S=i[f];S===-1?((p&n)===0||(p&l)!==0)&&(i[f]=xy(p,t)):S<=t&&(e.expiredLanes|=p),r&=~p}if(t=qe,n=Ne,n=Zi(e,e===t?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,n===0||e===t&&(Ue===2||Ue===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&qt(l),e.callbackNode=null,e.callbackPriority=0;if((n&3)===0||Ua(e,n)){if(t=n&-n,t===e.callbackPriority)return t;switch(l!==null&&qt(l),zr(n)){case 2:case 8:n=Fs;break;case 32:n=Vi;break;case 268435456:n=Ws;break;default:n=Vi}return l=ch.bind(null,e),n=rt(n,l),e.callbackPriority=t,e.callbackNode=n,t}return l!==null&&l!==null&&qt(l),e.callbackPriority=2,e.callbackNode=null,2}function ch(e,t){if(it!==0&&it!==5)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ko()&&e.callbackNode!==n)return null;var l=Ne;return l=Zi(e,e===qe?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(Xm(e,l,t),rh(e,mt()),e.callbackNode!=null&&e.callbackNode===n?ch.bind(null,e):null)}function uh(e,t){if(ko())return null;Xm(e,t,!0)}function $0(){ib(function(){(ze&6)!==0?rt(Ps,J0):oh()})}function xu(){return El===0&&(El=Is()),El}function sh(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Fi(""+e)}function fh(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}function P0(e,t,n,l,i){if(t==="submit"&&n&&n.stateNode===i){var r=sh((i[ht]||null).action),f=l.submitter;f&&(t=(t=f[ht]||null)?sh(t.formAction):f.getAttribute("formAction"),t!==null&&(r=t,f=null));var p=new to("action","action",null,l,i);e.push({event:p,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(El!==0){var S=f?fh(i,f):new FormData(i);qc(n,{pending:!0,data:S,method:i.method,action:r},null,S)}}else typeof r=="function"&&(p.preventDefault(),S=f?fh(i,f):new FormData(i),qc(n,{pending:!0,data:S,method:i.method,action:r},r,S))},currentTarget:i}]})}}for(var Su=0;Su<ic.length;Su++){var wu=ic[Su],F0=wu.toLowerCase(),W0=wu[0].toUpperCase()+wu.slice(1);Gt(F0,"on"+W0)}Gt(Xf,"onAnimationEnd"),Gt(Qf,"onAnimationIteration"),Gt(Zf,"onAnimationStart"),Gt("dblclick","onDoubleClick"),Gt("focusin","onFocus"),Gt("focusout","onBlur"),Gt(v0,"onTransitionRun"),Gt(p0,"onTransitionStart"),Gt(g0,"onTransitionCancel"),Gt(Kf,"onTransitionEnd"),Ul("onMouseEnter",["mouseout","mouseover"]),Ul("onMouseLeave",["mouseout","mouseover"]),Ul("onPointerEnter",["pointerout","pointerover"]),Ul("onPointerLeave",["pointerout","pointerover"]),rl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),rl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),rl("onBeforeInput",["compositionend","keypress","textInput","paste"]),rl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),rl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),rl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var xi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),I0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(xi));function dh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var l=e[n],i=l.event;l=l.listeners;e:{var r=void 0;if(t)for(var f=l.length-1;0<=f;f--){var p=l[f],S=p.instance,j=p.currentTarget;if(p=p.listener,S!==r&&i.isPropagationStopped())break e;r=p,i.currentTarget=j;try{r(i)}catch(q){Co(q)}i.currentTarget=null,r=S}else for(f=0;f<l.length;f++){if(p=l[f],S=p.instance,j=p.currentTarget,p=p.listener,S!==r&&i.isPropagationStopped())break e;r=p,i.currentTarget=j;try{r(i)}catch(q){Co(q)}i.currentTarget=null,r=S}}}}function Ee(e,t){var n=t[Ur];n===void 0&&(n=t[Ur]=new Set);var l=e+"__bubble";n.has(l)||(mh(t,e,2,!1),n.add(l))}function Eu(e,t,n){var l=0;t&&(l|=4),mh(n,e,l,t)}var Yo="_reactListening"+Math.random().toString(36).slice(2);function Tu(e){if(!e[Yo]){e[Yo]=!0,of.forEach(function(n){n!=="selectionchange"&&(I0.has(n)||Eu(n,!1,e),Eu(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Yo]||(t[Yo]=!0,Eu("selectionchange",!1,t))}}function mh(e,t,n,l){switch(Bh(t)){case 2:var i=Nb;break;case 8:i=Rb;break;default:i=Lu}n=i.bind(null,t,n,e),i=void 0,!Zr||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),l?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Au(e,t,n,l,i){var r=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var p=l.stateNode.containerInfo;if(p===i)break;if(f===4)for(f=l.return;f!==null;){var S=f.tag;if((S===3||S===4)&&f.stateNode.containerInfo===i)return;f=f.return}for(;p!==null;){if(f=_l(p),f===null)return;if(S=f.tag,S===5||S===6||S===26||S===27){l=r=f;continue e}p=p.parentNode}}l=l.return}xf(function(){var j=r,q=Xr(n),X=[];e:{var U=Jf.get(e);if(U!==void 0){var H=to,de=e;switch(e){case"keypress":if(Ii(n)===0)break e;case"keydown":case"keyup":H=Ky;break;case"focusin":de="focus",H=Pr;break;case"focusout":de="blur",H=Pr;break;case"beforeblur":case"afterblur":H=Pr;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":H=Ef;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":H=Uy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":H=Py;break;case Xf:case Qf:case Zf:H=Ly;break;case Kf:H=Wy;break;case"scroll":case"scrollend":H=jy;break;case"wheel":H=e0;break;case"copy":case"cut":case"paste":H=qy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":H=Af;break;case"toggle":case"beforetoggle":H=n0}var re=(t&4)!==0,Le=!re&&(e==="scroll"||e==="scrollend"),C=re?U!==null?U+"Capture":null:U;re=[];for(var A=j,O;A!==null;){var Y=A;if(O=Y.stateNode,Y=Y.tag,Y!==5&&Y!==26&&Y!==27||O===null||C===null||(Y=ka(A,C),Y!=null&&re.push(Si(A,Y,O))),Le)break;A=A.return}0<re.length&&(U=new H(U,de,null,n,q),X.push({event:U,listeners:re}))}}if((t&7)===0){e:{if(U=e==="mouseover"||e==="pointerover",H=e==="mouseout"||e==="pointerout",U&&n!==Vr&&(de=n.relatedTarget||n.fromElement)&&(_l(de)||de[Ol]))break e;if((H||U)&&(U=q.window===q?q:(U=q.ownerDocument)?U.defaultView||U.parentWindow:window,H?(de=n.relatedTarget||n.toElement,H=j,de=de?_l(de):null,de!==null&&(Le=d(de),re=de.tag,de!==Le||re!==5&&re!==27&&re!==6)&&(de=null)):(H=null,de=j),H!==de)){if(re=Ef,Y="onMouseLeave",C="onMouseEnter",A="mouse",(e==="pointerout"||e==="pointerover")&&(re=Af,Y="onPointerLeave",C="onPointerEnter",A="pointer"),Le=H==null?U:La(H),O=de==null?U:La(de),U=new re(Y,A+"leave",H,n,q),U.target=Le,U.relatedTarget=O,Y=null,_l(q)===j&&(re=new re(C,A+"enter",de,n,q),re.target=O,re.relatedTarget=Le,Y=re),Le=Y,H&&de)t:{for(re=H,C=de,A=0,O=re;O;O=da(O))A++;for(O=0,Y=C;Y;Y=da(Y))O++;for(;0<A-O;)re=da(re),A--;for(;0<O-A;)C=da(C),O--;for(;A--;){if(re===C||C!==null&&re===C.alternate)break t;re=da(re),C=da(C)}re=null}else re=null;H!==null&&hh(X,U,H,re,!1),de!==null&&Le!==null&&hh(X,Le,de,re,!0)}}e:{if(U=j?La(j):window,H=U.nodeName&&U.nodeName.toLowerCase(),H==="select"||H==="input"&&U.type==="file")var ee=jf;else if(Of(U))if(zf)ee=d0;else{ee=s0;var Se=u0}else H=U.nodeName,!H||H.toLowerCase()!=="input"||U.type!=="checkbox"&&U.type!=="radio"?j&&Yr(j.elementType)&&(ee=jf):ee=f0;if(ee&&(ee=ee(e,j))){_f(X,ee,n,q);break e}Se&&Se(e,U,j),e==="focusout"&&j&&U.type==="number"&&j.memoizedProps.value!=null&&Gr(U,"number",U.value)}switch(Se=j?La(j):window,e){case"focusin":(Of(Se)||Se.contentEditable==="true")&&(Yl=Se,nc=j,Ka=null);break;case"focusout":Ka=nc=Yl=null;break;case"mousedown":lc=!0;break;case"contextmenu":case"mouseup":case"dragend":lc=!1,Yf(X,n,q);break;case"selectionchange":if(h0)break;case"keydown":case"keyup":Yf(X,n,q)}var le;if(Wr)e:{switch(e){case"compositionstart":var ue="onCompositionStart";break e;case"compositionend":ue="onCompositionEnd";break e;case"compositionupdate":ue="onCompositionUpdate";break e}ue=void 0}else Gl?Df(e,n)&&(ue="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(ue="onCompositionStart");ue&&(Nf&&n.locale!=="ko"&&(Gl||ue!=="onCompositionStart"?ue==="onCompositionEnd"&&Gl&&(le=Sf()):(Dn=q,Kr="value"in Dn?Dn.value:Dn.textContent,Gl=!0)),Se=Vo(j,ue),0<Se.length&&(ue=new Tf(ue,e,null,n,q),X.push({event:ue,listeners:Se}),le?ue.data=le:(le=Mf(n),le!==null&&(ue.data=le)))),(le=a0?i0(e,n):o0(e,n))&&(ue=Vo(j,"onBeforeInput"),0<ue.length&&(Se=new Tf("onBeforeInput","beforeinput",null,n,q),X.push({event:Se,listeners:ue}),Se.data=le)),P0(X,e,j,n,q)}dh(X,t)})}function Si(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Vo(e,t){for(var n=t+"Capture",l=[];e!==null;){var i=e,r=i.stateNode;if(i=i.tag,i!==5&&i!==26&&i!==27||r===null||(i=ka(e,n),i!=null&&l.unshift(Si(e,i,r)),i=ka(e,t),i!=null&&l.push(Si(e,i,r))),e.tag===3)return l;e=e.return}return[]}function da(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function hh(e,t,n,l,i){for(var r=t._reactName,f=[];n!==null&&n!==l;){var p=n,S=p.alternate,j=p.stateNode;if(p=p.tag,S!==null&&S===l)break;p!==5&&p!==26&&p!==27||j===null||(S=j,i?(j=ka(n,r),j!=null&&f.unshift(Si(n,j,S))):i||(j=ka(n,r),j!=null&&f.push(Si(n,j,S)))),n=n.return}f.length!==0&&e.push({event:t,listeners:f})}var eb=/\r\n?/g,tb=/\u0000|\uFFFD/g;function vh(e){return(typeof e=="string"?e:""+e).replace(eb,`
`).replace(tb,"")}function ph(e,t){return t=vh(t),vh(e)===t}function Xo(){}function Be(e,t,n,l,i,r){switch(n){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||Ll(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&Ll(e,""+l);break;case"className":Ji(e,"class",l);break;case"tabIndex":Ji(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Ji(e,n,l);break;case"style":yf(e,l,r);break;case"data":if(t!=="object"){Ji(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||n!=="href")){e.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=Fi(""+l),e.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof r=="function"&&(n==="formAction"?(t!=="input"&&Be(e,t,"name",i.name,i,null),Be(e,t,"formEncType",i.formEncType,i,null),Be(e,t,"formMethod",i.formMethod,i,null),Be(e,t,"formTarget",i.formTarget,i,null)):(Be(e,t,"encType",i.encType,i,null),Be(e,t,"method",i.method,i,null),Be(e,t,"target",i.target,i,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(n);break}l=Fi(""+l),e.setAttribute(n,l);break;case"onClick":l!=null&&(e.onclick=Xo);break;case"onScroll":l!=null&&Ee("scroll",e);break;case"onScrollEnd":l!=null&&Ee("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=n}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}n=Fi(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""+l):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":l===!0?e.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(n,l):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(n,l):e.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(n):e.setAttribute(n,l);break;case"popover":Ee("beforetoggle",e),Ee("toggle",e),Ki(e,"popover",l);break;case"xlinkActuate":un(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":un(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":un(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":un(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":un(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":un(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":un(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":un(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":un(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Ki(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Oy.get(n)||n,Ki(e,n,l))}}function Nu(e,t,n,l,i,r){switch(n){case"style":yf(e,l,r);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(c(61));if(n=l.__html,n!=null){if(i.children!=null)throw Error(c(60));e.innerHTML=n}}break;case"children":typeof l=="string"?Ll(e,l):(typeof l=="number"||typeof l=="bigint")&&Ll(e,""+l);break;case"onScroll":l!=null&&Ee("scroll",e);break;case"onScrollEnd":l!=null&&Ee("scrollend",e);break;case"onClick":l!=null&&(e.onclick=Xo);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!rf.hasOwnProperty(n))e:{if(n[0]==="o"&&n[1]==="n"&&(i=n.endsWith("Capture"),t=n.slice(2,i?n.length-7:void 0),r=e[ht]||null,r=r!=null?r[n]:null,typeof r=="function"&&e.removeEventListener(t,r,i),typeof l=="function")){typeof r!="function"&&r!==null&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,l,i);break e}n in e?e[n]=l:l===!0?e.setAttribute(n,""):Ki(e,n,l)}}}function ot(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ee("error",e),Ee("load",e);var l=!1,i=!1,r;for(r in n)if(n.hasOwnProperty(r)){var f=n[r];if(f!=null)switch(r){case"src":l=!0;break;case"srcSet":i=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Be(e,t,r,f,n,null)}}i&&Be(e,t,"srcSet",n.srcSet,n,null),l&&Be(e,t,"src",n.src,n,null);return;case"input":Ee("invalid",e);var p=r=f=i=null,S=null,j=null;for(l in n)if(n.hasOwnProperty(l)){var q=n[l];if(q!=null)switch(l){case"name":i=q;break;case"type":f=q;break;case"checked":S=q;break;case"defaultChecked":j=q;break;case"value":r=q;break;case"defaultValue":p=q;break;case"children":case"dangerouslySetInnerHTML":if(q!=null)throw Error(c(137,t));break;default:Be(e,t,l,q,n,null)}}hf(e,r,p,S,j,f,i,!1),$i(e);return;case"select":Ee("invalid",e),l=f=r=null;for(i in n)if(n.hasOwnProperty(i)&&(p=n[i],p!=null))switch(i){case"value":r=p;break;case"defaultValue":f=p;break;case"multiple":l=p;default:Be(e,t,i,p,n,null)}t=r,n=f,e.multiple=!!l,t!=null?Bl(e,!!l,t,!1):n!=null&&Bl(e,!!l,n,!0);return;case"textarea":Ee("invalid",e),r=i=l=null;for(f in n)if(n.hasOwnProperty(f)&&(p=n[f],p!=null))switch(f){case"value":l=p;break;case"defaultValue":i=p;break;case"children":r=p;break;case"dangerouslySetInnerHTML":if(p!=null)throw Error(c(91));break;default:Be(e,t,f,p,n,null)}pf(e,l,i,r),$i(e);return;case"option":for(S in n)if(n.hasOwnProperty(S)&&(l=n[S],l!=null))switch(S){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Be(e,t,S,l,n,null)}return;case"dialog":Ee("beforetoggle",e),Ee("toggle",e),Ee("cancel",e),Ee("close",e);break;case"iframe":case"object":Ee("load",e);break;case"video":case"audio":for(l=0;l<xi.length;l++)Ee(xi[l],e);break;case"image":Ee("error",e),Ee("load",e);break;case"details":Ee("toggle",e);break;case"embed":case"source":case"link":Ee("error",e),Ee("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(j in n)if(n.hasOwnProperty(j)&&(l=n[j],l!=null))switch(j){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:Be(e,t,j,l,n,null)}return;default:if(Yr(t)){for(q in n)n.hasOwnProperty(q)&&(l=n[q],l!==void 0&&Nu(e,t,q,l,n,void 0));return}}for(p in n)n.hasOwnProperty(p)&&(l=n[p],l!=null&&Be(e,t,p,l,n,null))}function nb(e,t,n,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var i=null,r=null,f=null,p=null,S=null,j=null,q=null;for(H in n){var X=n[H];if(n.hasOwnProperty(H)&&X!=null)switch(H){case"checked":break;case"value":break;case"defaultValue":S=X;default:l.hasOwnProperty(H)||Be(e,t,H,null,l,X)}}for(var U in l){var H=l[U];if(X=n[U],l.hasOwnProperty(U)&&(H!=null||X!=null))switch(U){case"type":r=H;break;case"name":i=H;break;case"checked":j=H;break;case"defaultChecked":q=H;break;case"value":f=H;break;case"defaultValue":p=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(c(137,t));break;default:H!==X&&Be(e,t,U,H,l,X)}}qr(e,f,p,S,j,q,r,i);return;case"select":H=f=p=U=null;for(r in n)if(S=n[r],n.hasOwnProperty(r)&&S!=null)switch(r){case"value":break;case"multiple":H=S;default:l.hasOwnProperty(r)||Be(e,t,r,null,l,S)}for(i in l)if(r=l[i],S=n[i],l.hasOwnProperty(i)&&(r!=null||S!=null))switch(i){case"value":U=r;break;case"defaultValue":p=r;break;case"multiple":f=r;default:r!==S&&Be(e,t,i,r,l,S)}t=p,n=f,l=H,U!=null?Bl(e,!!n,U,!1):!!l!=!!n&&(t!=null?Bl(e,!!n,t,!0):Bl(e,!!n,n?[]:"",!1));return;case"textarea":H=U=null;for(p in n)if(i=n[p],n.hasOwnProperty(p)&&i!=null&&!l.hasOwnProperty(p))switch(p){case"value":break;case"children":break;default:Be(e,t,p,null,l,i)}for(f in l)if(i=l[f],r=n[f],l.hasOwnProperty(f)&&(i!=null||r!=null))switch(f){case"value":U=i;break;case"defaultValue":H=i;break;case"children":break;case"dangerouslySetInnerHTML":if(i!=null)throw Error(c(91));break;default:i!==r&&Be(e,t,f,i,l,r)}vf(e,U,H);return;case"option":for(var de in n)if(U=n[de],n.hasOwnProperty(de)&&U!=null&&!l.hasOwnProperty(de))switch(de){case"selected":e.selected=!1;break;default:Be(e,t,de,null,l,U)}for(S in l)if(U=l[S],H=n[S],l.hasOwnProperty(S)&&U!==H&&(U!=null||H!=null))switch(S){case"selected":e.selected=U&&typeof U!="function"&&typeof U!="symbol";break;default:Be(e,t,S,U,l,H)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var re in n)U=n[re],n.hasOwnProperty(re)&&U!=null&&!l.hasOwnProperty(re)&&Be(e,t,re,null,l,U);for(j in l)if(U=l[j],H=n[j],l.hasOwnProperty(j)&&U!==H&&(U!=null||H!=null))switch(j){case"children":case"dangerouslySetInnerHTML":if(U!=null)throw Error(c(137,t));break;default:Be(e,t,j,U,l,H)}return;default:if(Yr(t)){for(var Le in n)U=n[Le],n.hasOwnProperty(Le)&&U!==void 0&&!l.hasOwnProperty(Le)&&Nu(e,t,Le,void 0,l,U);for(q in l)U=l[q],H=n[q],!l.hasOwnProperty(q)||U===H||U===void 0&&H===void 0||Nu(e,t,q,U,l,H);return}}for(var C in n)U=n[C],n.hasOwnProperty(C)&&U!=null&&!l.hasOwnProperty(C)&&Be(e,t,C,null,l,U);for(X in l)U=l[X],H=n[X],!l.hasOwnProperty(X)||U===H||U==null&&H==null||Be(e,t,X,U,l,H)}var Ru=null,Cu=null;function Qo(e){return e.nodeType===9?e:e.ownerDocument}function gh(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function yh(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Du(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Mu=null;function lb(){var e=window.event;return e&&e.type==="popstate"?e===Mu?!1:(Mu=e,!0):(Mu=null,!1)}var bh=typeof setTimeout=="function"?setTimeout:void 0,ab=typeof clearTimeout=="function"?clearTimeout:void 0,xh=typeof Promise=="function"?Promise:void 0,ib=typeof queueMicrotask=="function"?queueMicrotask:typeof xh<"u"?function(e){return xh.resolve(null).then(e).catch(ob)}:bh;function ob(e){setTimeout(function(){throw e})}function Qn(e){return e==="head"}function Sh(e,t){var n=t,l=0,i=0;do{var r=n.nextSibling;if(e.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(0<l&&8>l){n=l;var f=e.ownerDocument;if(n&1&&wi(f.documentElement),n&2&&wi(f.body),n&4)for(n=f.head,wi(n),f=n.firstChild;f;){var p=f.nextSibling,S=f.nodeName;f[Ba]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&f.rel.toLowerCase()==="stylesheet"||n.removeChild(f),f=p}}if(i===0){e.removeChild(r),Mi(t);return}i--}else n==="$"||n==="$?"||n==="$!"?i++:l=n.charCodeAt(0)-48;else l=0;n=r}while(n);Mi(t)}function Ou(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":Ou(n),Hr(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}e.removeChild(n)}}function rb(e,t,n,l){for(;e.nodeType===1;){var i=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[Ba])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(r=e.getAttribute("rel"),r==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(r!==i.rel||e.getAttribute("href")!==(i.href==null||i.href===""?null:i.href)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin)||e.getAttribute("title")!==(i.title==null?null:i.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(r=e.getAttribute("src"),(r!==(i.src==null?null:i.src)||e.getAttribute("type")!==(i.type==null?null:i.type)||e.getAttribute("crossorigin")!==(i.crossOrigin==null?null:i.crossOrigin))&&r&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var r=i.name==null?null:""+i.name;if(i.type==="hidden"&&e.getAttribute("name")===r)return e}else return e;if(e=Vt(e.nextSibling),e===null)break}return null}function cb(e,t,n){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!n||(e=Vt(e.nextSibling),e===null))return null;return e}function _u(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function ub(e,t){var n=e.ownerDocument;if(e.data!=="$?"||n.readyState==="complete")t();else{var l=function(){t(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Vt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var ju=null;function wh(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}function Eh(e,t,n){switch(t=Qo(n),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function wi(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Hr(e)}var Lt=new Map,Th=new Set;function Zo(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var En=Q.d;Q.d={f:sb,r:fb,D:db,C:mb,L:hb,m:vb,X:gb,S:pb,M:yb};function sb(){var e=En.f(),t=Bo();return e||t}function fb(e){var t=jl(e);t!==null&&t.tag===5&&t.type==="form"?Xd(t):En.r(e)}var ma=typeof document>"u"?null:document;function Ah(e,t,n){var l=ma;if(l&&typeof t=="string"&&t){var i=Ot(t);i='link[rel="'+e+'"][href="'+i+'"]',typeof n=="string"&&(i+='[crossorigin="'+n+'"]'),Th.has(i)||(Th.add(i),e={rel:e,crossOrigin:n,href:t},l.querySelector(i)===null&&(t=l.createElement("link"),ot(t,"link",e),et(t),l.head.appendChild(t)))}}function db(e){En.D(e),Ah("dns-prefetch",e,null)}function mb(e,t){En.C(e,t),Ah("preconnect",e,t)}function hb(e,t,n){En.L(e,t,n);var l=ma;if(l&&e&&t){var i='link[rel="preload"][as="'+Ot(t)+'"]';t==="image"&&n&&n.imageSrcSet?(i+='[imagesrcset="'+Ot(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(i+='[imagesizes="'+Ot(n.imageSizes)+'"]')):i+='[href="'+Ot(e)+'"]';var r=i;switch(t){case"style":r=ha(e);break;case"script":r=va(e)}Lt.has(r)||(e=x({rel:"preload",href:t==="image"&&n&&n.imageSrcSet?void 0:e,as:t},n),Lt.set(r,e),l.querySelector(i)!==null||t==="style"&&l.querySelector(Ei(r))||t==="script"&&l.querySelector(Ti(r))||(t=l.createElement("link"),ot(t,"link",e),et(t),l.head.appendChild(t)))}}function vb(e,t){En.m(e,t);var n=ma;if(n&&e){var l=t&&typeof t.as=="string"?t.as:"script",i='link[rel="modulepreload"][as="'+Ot(l)+'"][href="'+Ot(e)+'"]',r=i;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":r=va(e)}if(!Lt.has(r)&&(e=x({rel:"modulepreload",href:e},t),Lt.set(r,e),n.querySelector(i)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ti(r)))return}l=n.createElement("link"),ot(l,"link",e),et(l),n.head.appendChild(l)}}}function pb(e,t,n){En.S(e,t,n);var l=ma;if(l&&e){var i=zl(l).hoistableStyles,r=ha(e);t=t||"default";var f=i.get(r);if(!f){var p={loading:0,preload:null};if(f=l.querySelector(Ei(r)))p.loading=5;else{e=x({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Lt.get(r))&&zu(e,n);var S=f=l.createElement("link");et(S),ot(S,"link",e),S._p=new Promise(function(j,q){S.onload=j,S.onerror=q}),S.addEventListener("load",function(){p.loading|=1}),S.addEventListener("error",function(){p.loading|=2}),p.loading|=4,Ko(f,t,l)}f={type:"stylesheet",instance:f,count:1,state:p},i.set(r,f)}}}function gb(e,t){En.X(e,t);var n=ma;if(n&&e){var l=zl(n).hoistableScripts,i=va(e),r=l.get(i);r||(r=n.querySelector(Ti(i)),r||(e=x({src:e,async:!0},t),(t=Lt.get(i))&&Uu(e,t),r=n.createElement("script"),et(r),ot(r,"link",e),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function yb(e,t){En.M(e,t);var n=ma;if(n&&e){var l=zl(n).hoistableScripts,i=va(e),r=l.get(i);r||(r=n.querySelector(Ti(i)),r||(e=x({src:e,async:!0,type:"module"},t),(t=Lt.get(i))&&Uu(e,t),r=n.createElement("script"),et(r),ot(r,"link",e),n.head.appendChild(r)),r={type:"script",instance:r,count:1,state:null},l.set(i,r))}}function Nh(e,t,n,l){var i=(i=ie.current)?Zo(i):null;if(!i)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(t=ha(n.href),n=zl(i).hoistableStyles,l=n.get(t),l||(l={type:"style",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){e=ha(n.href);var r=zl(i).hoistableStyles,f=r.get(e);if(f||(i=i.ownerDocument||i,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},r.set(e,f),(r=i.querySelector(Ei(e)))&&!r._p&&(f.instance=r,f.state.loading=5),Lt.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Lt.set(e,n),r||bb(i,e,n,f.state))),t&&l===null)throw Error(c(528,""));return f}if(t&&l!==null)throw Error(c(529,""));return null;case"script":return t=n.async,n=n.src,typeof n=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=va(n),n=zl(i).hoistableScripts,l=n.get(t),l||(l={type:"script",instance:null,count:0,state:null},n.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function ha(e){return'href="'+Ot(e)+'"'}function Ei(e){return'link[rel="stylesheet"]['+e+"]"}function Rh(e){return x({},e,{"data-precedence":e.precedence,precedence:null})}function bb(e,t,n,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),ot(t,"link",n),et(t),e.head.appendChild(t))}function va(e){return'[src="'+Ot(e)+'"]'}function Ti(e){return"script[async]"+e}function Ch(e,t,n){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Ot(n.href)+'"]');if(l)return t.instance=l,et(l),l;var i=x({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),et(l),ot(l,"style",i),Ko(l,n.precedence,e),t.instance=l;case"stylesheet":i=ha(n.href);var r=e.querySelector(Ei(i));if(r)return t.state.loading|=4,t.instance=r,et(r),r;l=Rh(n),(i=Lt.get(i))&&zu(l,i),r=(e.ownerDocument||e).createElement("link"),et(r);var f=r;return f._p=new Promise(function(p,S){f.onload=p,f.onerror=S}),ot(r,"link",l),t.state.loading|=4,Ko(r,n.precedence,e),t.instance=r;case"script":return r=va(n.src),(i=e.querySelector(Ti(r)))?(t.instance=i,et(i),i):(l=n,(i=Lt.get(r))&&(l=x({},n),Uu(l,i)),e=e.ownerDocument||e,i=e.createElement("script"),et(i),ot(i,"link",l),e.head.appendChild(i),t.instance=i);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,Ko(l,n.precedence,e));return t.instance}function Ko(e,t,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),i=l.length?l[l.length-1]:null,r=i,f=0;f<l.length;f++){var p=l[f];if(p.dataset.precedence===t)r=p;else if(r!==i)break}r?r.parentNode.insertBefore(e,r.nextSibling):(t=n.nodeType===9?n.head:n,t.insertBefore(e,t.firstChild))}function zu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Uu(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Jo=null;function Dh(e,t,n){if(Jo===null){var l=new Map,i=Jo=new Map;i.set(n,l)}else i=Jo,l=i.get(n),l||(l=new Map,i.set(n,l));if(l.has(e))return l;for(l.set(e,null),n=n.getElementsByTagName(e),i=0;i<n.length;i++){var r=n[i];if(!(r[Ba]||r[ct]||e==="link"&&r.getAttribute("rel")==="stylesheet")&&r.namespaceURI!=="http://www.w3.org/2000/svg"){var f=r.getAttribute(t)||"";f=e+f;var p=l.get(f);p?p.push(r):l.set(f,[r])}}return l}function Mh(e,t,n){e=e.ownerDocument||e,e.head.insertBefore(n,t==="title"?e.querySelector("head > title"):null)}function xb(e,t,n){if(n===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Oh(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Ai=null;function Sb(){}function wb(e,t,n){if(Ai===null)throw Error(c(475));var l=Ai;if(t.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var i=ha(n.href),r=e.querySelector(Ei(i));if(r){e=r._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=$o.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=r,et(r);return}r=e.ownerDocument||e,n=Rh(n),(i=Lt.get(i))&&zu(n,i),r=r.createElement("link"),et(r);var f=r;f._p=new Promise(function(p,S){f.onload=p,f.onerror=S}),ot(r,"link",n),t.instance=r}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=$o.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function Eb(){if(Ai===null)throw Error(c(475));var e=Ai;return e.stylesheets&&e.count===0&&Hu(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Hu(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}function $o(){if(this.count--,this.count===0){if(this.stylesheets)Hu(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Po=null;function Hu(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Po=new Map,t.forEach(Tb,e),Po=null,$o.call(e))}function Tb(e,t){if(!(t.state.loading&4)){var n=Po.get(e);if(n)var l=n.get(null);else{n=new Map,Po.set(e,n);for(var i=e.querySelectorAll("link[data-precedence],style[data-precedence]"),r=0;r<i.length;r++){var f=i[r];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(n.set(f.dataset.precedence,f),l=f)}l&&n.set(null,l)}i=t.instance,f=i.getAttribute("data-precedence"),r=n.get(f)||l,r===l&&n.set(null,i),n.set(f,i),this.count++,l=$o.bind(this),i.addEventListener("load",l),i.addEventListener("error",l),r?r.parentNode.insertBefore(i,r.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(i,e.firstChild)),t.state.loading|=4}}var Ni={$$typeof:_,Provider:null,Consumer:null,_currentValue:B,_currentValue2:B,_threadCount:0};function Ab(e,t,n,l,i,r,f,p){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=_r(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=_r(0),this.hiddenUpdates=_r(null),this.identifierPrefix=l,this.onUncaughtError=i,this.onCaughtError=r,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=p,this.incompleteTransitions=new Map}function _h(e,t,n,l,i,r,f,p,S,j,q,X){return e=new Ab(e,t,n,f,p,S,j,X),t=1,r===!0&&(t|=24),r=wt(3,null,null,t),e.current=r,r.stateNode=e,t=gc(),t.refCount++,e.pooledCache=t,t.refCount++,r.memoizedState={element:l,isDehydrated:n,cache:t},Sc(r),e}function jh(e){return e?(e=Zl,e):Zl}function zh(e,t,n,l,i,r){i=jh(i),l.context===null?l.context=i:l.pendingContext=i,l=_n(t),l.payload={element:n},r=r===void 0?null:r,r!==null&&(l.callback=r),n=jn(e,l,t),n!==null&&(Rt(n,e,t),ni(n,e,t))}function Uh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Bu(e,t){Uh(e,t),(e=e.alternate)&&Uh(e,t)}function Hh(e){if(e.tag===13){var t=Ql(e,67108864);t!==null&&Rt(t,e,67108864),Bu(e,67108864)}}var Fo=!0;function Nb(e,t,n,l){var i=M.T;M.T=null;var r=Q.p;try{Q.p=2,Lu(e,t,n,l)}finally{Q.p=r,M.T=i}}function Rb(e,t,n,l){var i=M.T;M.T=null;var r=Q.p;try{Q.p=8,Lu(e,t,n,l)}finally{Q.p=r,M.T=i}}function Lu(e,t,n,l){if(Fo){var i=ku(l);if(i===null)Au(e,t,l,Wo,n),Lh(e,l);else if(Db(i,e,t,n,l))l.stopPropagation();else if(Lh(e,l),t&4&&-1<Cb.indexOf(e)){for(;i!==null;){var r=jl(i);if(r!==null)switch(r.tag){case 3:if(r=r.stateNode,r.current.memoizedState.isDehydrated){var f=ol(r.pendingLanes);if(f!==0){var p=r;for(p.pendingLanes|=2,p.entangledLanes|=2;f;){var S=1<<31-xt(f);p.entanglements[1]|=S,f&=~S}nn(r),(ze&6)===0&&(Uo=mt()+500,bi(0))}}break;case 13:p=Ql(r,2),p!==null&&Rt(p,r,2),Bo(),Bu(r,2)}if(r=ku(l),r===null&&Au(e,t,l,Wo,n),r===i)break;i=r}i!==null&&l.stopPropagation()}else Au(e,t,l,null,n)}}function ku(e){return e=Xr(e),qu(e)}var Wo=null;function qu(e){if(Wo=null,e=_l(e),e!==null){var t=d(e);if(t===null)e=null;else{var n=t.tag;if(n===13){if(e=h(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Wo=e,null}function Bh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(my()){case Ps:return 2;case Fs:return 8;case Vi:case hy:return 32;case Ws:return 268435456;default:return 32}default:return 32}}var Gu=!1,Zn=null,Kn=null,Jn=null,Ri=new Map,Ci=new Map,$n=[],Cb="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Lh(e,t){switch(e){case"focusin":case"focusout":Zn=null;break;case"dragenter":case"dragleave":Kn=null;break;case"mouseover":case"mouseout":Jn=null;break;case"pointerover":case"pointerout":Ri.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ci.delete(t.pointerId)}}function Di(e,t,n,l,i,r){return e===null||e.nativeEvent!==r?(e={blockedOn:t,domEventName:n,eventSystemFlags:l,nativeEvent:r,targetContainers:[i]},t!==null&&(t=jl(t),t!==null&&Hh(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Db(e,t,n,l,i){switch(t){case"focusin":return Zn=Di(Zn,e,t,n,l,i),!0;case"dragenter":return Kn=Di(Kn,e,t,n,l,i),!0;case"mouseover":return Jn=Di(Jn,e,t,n,l,i),!0;case"pointerover":var r=i.pointerId;return Ri.set(r,Di(Ri.get(r)||null,e,t,n,l,i)),!0;case"gotpointercapture":return r=i.pointerId,Ci.set(r,Di(Ci.get(r)||null,e,t,n,l,i)),!0}return!1}function kh(e){var t=_l(e.target);if(t!==null){var n=d(t);if(n!==null){if(t=n.tag,t===13){if(t=h(n),t!==null){e.blockedOn=t,wy(e.priority,function(){if(n.tag===13){var l=Nt();l=jr(l);var i=Ql(n,l);i!==null&&Rt(i,n,l),Bu(n,l)}});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Io(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=ku(e.nativeEvent);if(n===null){n=e.nativeEvent;var l=new n.constructor(n.type,n);Vr=l,n.target.dispatchEvent(l),Vr=null}else return t=jl(n),t!==null&&Hh(t),e.blockedOn=n,!1;t.shift()}return!0}function qh(e,t,n){Io(e)&&n.delete(t)}function Mb(){Gu=!1,Zn!==null&&Io(Zn)&&(Zn=null),Kn!==null&&Io(Kn)&&(Kn=null),Jn!==null&&Io(Jn)&&(Jn=null),Ri.forEach(qh),Ci.forEach(qh)}function er(e,t){e.blockedOn===t&&(e.blockedOn=null,Gu||(Gu=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Mb)))}var tr=null;function Gh(e){tr!==e&&(tr=e,a.unstable_scheduleCallback(a.unstable_NormalPriority,function(){tr===e&&(tr=null);for(var t=0;t<e.length;t+=3){var n=e[t],l=e[t+1],i=e[t+2];if(typeof l!="function"){if(qu(l||n)===null)continue;break}var r=jl(n);r!==null&&(e.splice(t,3),t-=3,qc(r,{pending:!0,data:i,method:n.method,action:l},l,i))}}))}function Mi(e){function t(S){return er(S,e)}Zn!==null&&er(Zn,e),Kn!==null&&er(Kn,e),Jn!==null&&er(Jn,e),Ri.forEach(t),Ci.forEach(t);for(var n=0;n<$n.length;n++){var l=$n[n];l.blockedOn===e&&(l.blockedOn=null)}for(;0<$n.length&&(n=$n[0],n.blockedOn===null);)kh(n),n.blockedOn===null&&$n.shift();if(n=(e.ownerDocument||e).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var i=n[l],r=n[l+1],f=i[ht]||null;if(typeof r=="function")f||Gh(n);else if(f){var p=null;if(r&&r.hasAttribute("formAction")){if(i=r,f=r[ht]||null)p=f.formAction;else if(qu(i)!==null)continue}else p=f.action;typeof p=="function"?n[l+1]=p:(n.splice(l,3),l-=3),Gh(n)}}}function Yu(e){this._internalRoot=e}nr.prototype.render=Yu.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var n=t.current,l=Nt();zh(n,l,e,t,null,null)},nr.prototype.unmount=Yu.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;zh(e.current,2,null,e,null,null),Bo(),t[Ol]=null}};function nr(e){this._internalRoot=e}nr.prototype.unstable_scheduleHydration=function(e){if(e){var t=lf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<$n.length&&t!==0&&t<$n[n].priority;n++);$n.splice(n,0,e),n===0&&kh(e)}};var Yh=o.version;if(Yh!=="19.1.0")throw Error(c(527,Yh,"19.1.0"));Q.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=y(t),e=e!==null?g(e):null,e=e===null?null:e.stateNode,e};var Ob={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:M,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var lr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!lr.isDisabled&&lr.supportsFiber)try{za=lr.inject(Ob),bt=lr}catch{}}return _i.createRoot=function(e,t){if(!s(e))throw Error(c(299));var n=!1,l="",i=am,r=im,f=om,p=null;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(i=t.onUncaughtError),t.onCaughtError!==void 0&&(r=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(p=t.unstable_transitionCallbacks)),t=_h(e,1,!1,null,null,n,l,i,r,f,p,null),e[Ol]=t.current,Tu(e),new Yu(t)},_i.hydrateRoot=function(e,t,n){if(!s(e))throw Error(c(299));var l=!1,i="",r=am,f=im,p=om,S=null,j=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onUncaughtError!==void 0&&(r=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(p=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(S=n.unstable_transitionCallbacks),n.formState!==void 0&&(j=n.formState)),t=_h(e,1,!0,t,n??null,l,i,r,f,p,S,j),t.context=jh(null),n=t.current,l=Nt(),l=jr(l),i=_n(l),i.callback=null,jn(n,i,l),n=l,t.current.lanes=n,Ha(t,n),nn(t),e[Ol]=t.current,Tu(e),new nr(t)},_i.version="19.1.0",_i}var Wh;function Gb(){if(Wh)return Qu.exports;Wh=1;function a(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(a)}catch(o){console.error(o)}}return a(),Qu.exports=qb(),Qu.exports}var Yb=Gb();/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vb=a=>a.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Xb=a=>a.replace(/^([A-Z])|[\s-_]+(\w)/g,(o,u,c)=>c?c.toUpperCase():u.toLowerCase()),Ih=a=>{const o=Xb(a);return o.charAt(0).toUpperCase()+o.slice(1)},Lv=(...a)=>a.filter((o,u,c)=>!!o&&o.trim()!==""&&c.indexOf(o)===u).join(" ").trim(),Qb=a=>{for(const o in a)if(o.startsWith("aria-")||o==="role"||o==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Zb={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kb=b.forwardRef(({color:a="currentColor",size:o=24,strokeWidth:u=2,absoluteStrokeWidth:c,className:s="",children:d,iconNode:h,...v},y)=>b.createElement("svg",{ref:y,...Zb,width:o,height:o,stroke:a,strokeWidth:c?Number(u)*24/Number(o):u,className:Lv("lucide",s),...!d&&!Qb(v)&&{"aria-hidden":"true"},...v},[...h.map(([g,x])=>b.createElement(g,x)),...Array.isArray(d)?d:[d]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ge=(a,o)=>{const u=b.forwardRef(({className:c,...s},d)=>b.createElement(Kb,{ref:d,iconNode:o,className:Lv(`lucide-${Vb(Ih(a))}`,`lucide-${a}`,c),...s}));return u.displayName=Ih(a),u};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jb=[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]],$b=Ge("bell",Jb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pb=[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]],ev=Ge("building-2",Pb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fb=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],tv=Ge("calendar",Fb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wb=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],Ib=Ge("check",Wb);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ex=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],kv=Ge("chevron-down",ex);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tx=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],nx=Ge("chevron-up",tx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lx=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],nv=Ge("circle-check-big",lx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ax=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],lv=Ge("clock",ax);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ix=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],ox=Ge("download",ix);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rx=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],cx=Ge("file-text",rx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ux=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],sx=Ge("globe",ux);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fx=[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]],dx=Ge("hash",fx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mx=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],hx=Ge("log-out",mx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vx=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],px=Ge("mail",vx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gx=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],yx=Ge("map-pin",gx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bx=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],xx=Ge("pen-line",bx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sx=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],wx=Ge("phone",Sx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ex=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Tx=Ge("plus",Ex);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ax=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Nx=Ge("settings",Ax);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rx=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],Cx=Ge("trash-2",Rx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dx=[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]],Mx=Ge("trending-up",Dx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ox=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],dr=Ge("triangle-alert",Ox);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _x=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],$u=Ge("user",_x);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jx=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],zx=Ge("users",jx);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ux=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Hx=Ge("x",Ux);function av(a,o){if(typeof a=="function")return a(o);a!=null&&(a.current=o)}function qv(...a){return o=>{let u=!1;const c=a.map(s=>{const d=av(s,o);return!u&&typeof d=="function"&&(u=!0),d});if(u)return()=>{for(let s=0;s<c.length;s++){const d=c[s];typeof d=="function"?d():av(a[s],null)}}}}function Ze(...a){return b.useCallback(qv(...a),a)}function Ta(a){const o=Lx(a),u=b.forwardRef((c,s)=>{const{children:d,...h}=c,v=b.Children.toArray(d),y=v.find(qx);if(y){const g=y.props.children,x=v.map(w=>w===y?b.Children.count(g)>1?b.Children.only(null):b.isValidElement(g)?g.props.children:null:w);return m.jsx(o,{...h,ref:s,children:b.isValidElement(g)?b.cloneElement(g,void 0,x):null})}return m.jsx(o,{...h,ref:s,children:d})});return u.displayName=`${a}.Slot`,u}var Bx=Ta("Slot");function Lx(a){const o=b.forwardRef((u,c)=>{const{children:s,...d}=u;if(b.isValidElement(s)){const h=Yx(s),v=Gx(d,s.props);return s.type!==b.Fragment&&(v.ref=c?qv(c,h):h),b.cloneElement(s,v)}return b.Children.count(s)>1?b.Children.only(null):null});return o.displayName=`${a}.SlotClone`,o}var kx=Symbol("radix.slottable");function qx(a){return b.isValidElement(a)&&typeof a.type=="function"&&"__radixId"in a.type&&a.type.__radixId===kx}function Gx(a,o){const u={...o};for(const c in o){const s=a[c],d=o[c];/^on[A-Z]/.test(c)?s&&d?u[c]=(...v)=>{const y=d(...v);return s(...v),y}:s&&(u[c]=s):c==="style"?u[c]={...s,...d}:c==="className"&&(u[c]=[s,d].filter(Boolean).join(" "))}return{...a,...u}}function Yx(a){let o=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,u=o&&"isReactWarning"in o&&o.isReactWarning;return u?a.ref:(o=Object.getOwnPropertyDescriptor(a,"ref")?.get,u=o&&"isReactWarning"in o&&o.isReactWarning,u?a.props.ref:a.props.ref||a.ref)}function Gv(a){var o,u,c="";if(typeof a=="string"||typeof a=="number")c+=a;else if(typeof a=="object")if(Array.isArray(a)){var s=a.length;for(o=0;o<s;o++)a[o]&&(u=Gv(a[o]))&&(c&&(c+=" "),c+=u)}else for(u in a)a[u]&&(c&&(c+=" "),c+=u);return c}function Yv(){for(var a,o,u=0,c="",s=arguments.length;u<s;u++)(a=arguments[u])&&(o=Gv(a))&&(c&&(c+=" "),c+=o);return c}const iv=a=>typeof a=="boolean"?`${a}`:a===0?"0":a,ov=Yv,As=(a,o)=>u=>{var c;if(o?.variants==null)return ov(a,u?.class,u?.className);const{variants:s,defaultVariants:d}=o,h=Object.keys(s).map(g=>{const x=u?.[g],w=d?.[g];if(x===null)return null;const R=iv(x)||iv(w);return s[g][R]}),v=u&&Object.entries(u).reduce((g,x)=>{let[w,R]=x;return R===void 0||(g[w]=R),g},{}),y=o==null||(c=o.compoundVariants)===null||c===void 0?void 0:c.reduce((g,x)=>{let{class:w,className:R,...N}=x;return Object.entries(N).every(z=>{let[T,D]=z;return Array.isArray(D)?D.includes({...d,...v}[T]):{...d,...v}[T]===D})?[...g,w,R]:g},[]);return ov(a,h,y,u?.class,u?.className)},Ns="-",Vx=a=>{const o=Qx(a),{conflictingClassGroups:u,conflictingClassGroupModifiers:c}=a;return{getClassGroupId:h=>{const v=h.split(Ns);return v[0]===""&&v.length!==1&&v.shift(),Vv(v,o)||Xx(h)},getConflictingClassGroupIds:(h,v)=>{const y=u[h]||[];return v&&c[h]?[...y,...c[h]]:y}}},Vv=(a,o)=>{if(a.length===0)return o.classGroupId;const u=a[0],c=o.nextPart.get(u),s=c?Vv(a.slice(1),c):void 0;if(s)return s;if(o.validators.length===0)return;const d=a.join(Ns);return o.validators.find(({validator:h})=>h(d))?.classGroupId},rv=/^\[(.+)\]$/,Xx=a=>{if(rv.test(a)){const o=rv.exec(a)[1],u=o?.substring(0,o.indexOf(":"));if(u)return"arbitrary.."+u}},Qx=a=>{const{theme:o,classGroups:u}=a,c={nextPart:new Map,validators:[]};for(const s in u)cs(u[s],c,s,o);return c},cs=(a,o,u,c)=>{a.forEach(s=>{if(typeof s=="string"){const d=s===""?o:cv(o,s);d.classGroupId=u;return}if(typeof s=="function"){if(Zx(s)){cs(s(c),o,u,c);return}o.validators.push({validator:s,classGroupId:u});return}Object.entries(s).forEach(([d,h])=>{cs(h,cv(o,d),u,c)})})},cv=(a,o)=>{let u=a;return o.split(Ns).forEach(c=>{u.nextPart.has(c)||u.nextPart.set(c,{nextPart:new Map,validators:[]}),u=u.nextPart.get(c)}),u},Zx=a=>a.isThemeGetter,Kx=a=>{if(a<1)return{get:()=>{},set:()=>{}};let o=0,u=new Map,c=new Map;const s=(d,h)=>{u.set(d,h),o++,o>a&&(o=0,c=u,u=new Map)};return{get(d){let h=u.get(d);if(h!==void 0)return h;if((h=c.get(d))!==void 0)return s(d,h),h},set(d,h){u.has(d)?u.set(d,h):s(d,h)}}},us="!",ss=":",Jx=ss.length,$x=a=>{const{prefix:o,experimentalParseClassName:u}=a;let c=s=>{const d=[];let h=0,v=0,y=0,g;for(let z=0;z<s.length;z++){let T=s[z];if(h===0&&v===0){if(T===ss){d.push(s.slice(y,z)),y=z+Jx;continue}if(T==="/"){g=z;continue}}T==="["?h++:T==="]"?h--:T==="("?v++:T===")"&&v--}const x=d.length===0?s:s.substring(y),w=Px(x),R=w!==x,N=g&&g>y?g-y:void 0;return{modifiers:d,hasImportantModifier:R,baseClassName:w,maybePostfixModifierPosition:N}};if(o){const s=o+ss,d=c;c=h=>h.startsWith(s)?d(h.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:h,maybePostfixModifierPosition:void 0}}if(u){const s=c;c=d=>u({className:d,parseClassName:s})}return c},Px=a=>a.endsWith(us)?a.substring(0,a.length-1):a.startsWith(us)?a.substring(1):a,Fx=a=>{const o=Object.fromEntries(a.orderSensitiveModifiers.map(c=>[c,!0]));return c=>{if(c.length<=1)return c;const s=[];let d=[];return c.forEach(h=>{h[0]==="["||o[h]?(s.push(...d.sort(),h),d=[]):d.push(h)}),s.push(...d.sort()),s}},Wx=a=>({cache:Kx(a.cacheSize),parseClassName:$x(a),sortModifiers:Fx(a),...Vx(a)}),Ix=/\s+/,e1=(a,o)=>{const{parseClassName:u,getClassGroupId:c,getConflictingClassGroupIds:s,sortModifiers:d}=o,h=[],v=a.trim().split(Ix);let y="";for(let g=v.length-1;g>=0;g-=1){const x=v[g],{isExternal:w,modifiers:R,hasImportantModifier:N,baseClassName:z,maybePostfixModifierPosition:T}=u(x);if(w){y=x+(y.length>0?" "+y:y);continue}let D=!!T,L=c(D?z.substring(0,T):z);if(!L){if(!D){y=x+(y.length>0?" "+y:y);continue}if(L=c(z),!L){y=x+(y.length>0?" "+y:y);continue}D=!1}const k=d(R).join(":"),_=N?k+us:k,Z=_+L;if(h.includes(Z))continue;h.push(Z);const G=s(L,D);for(let I=0;I<G.length;++I){const $=G[I];h.push(_+$)}y=x+(y.length>0?" "+y:y)}return y};function t1(){let a=0,o,u,c="";for(;a<arguments.length;)(o=arguments[a++])&&(u=Xv(o))&&(c&&(c+=" "),c+=u);return c}const Xv=a=>{if(typeof a=="string")return a;let o,u="";for(let c=0;c<a.length;c++)a[c]&&(o=Xv(a[c]))&&(u&&(u+=" "),u+=o);return u};function n1(a,...o){let u,c,s,d=h;function h(y){const g=o.reduce((x,w)=>w(x),a());return u=Wx(g),c=u.cache.get,s=u.cache.set,d=v,v(y)}function v(y){const g=c(y);if(g)return g;const x=e1(y,u);return s(y,x),x}return function(){return d(t1.apply(null,arguments))}}const Ie=a=>{const o=u=>u[a]||[];return o.isThemeGetter=!0,o},Qv=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Zv=/^\((?:(\w[\w-]*):)?(.+)\)$/i,l1=/^\d+\/\d+$/,a1=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,i1=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,o1=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,r1=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,c1=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,pa=a=>l1.test(a),xe=a=>!!a&&!Number.isNaN(Number(a)),Fn=a=>!!a&&Number.isInteger(Number(a)),Pu=a=>a.endsWith("%")&&xe(a.slice(0,-1)),Tn=a=>a1.test(a),u1=()=>!0,s1=a=>i1.test(a)&&!o1.test(a),Kv=()=>!1,f1=a=>r1.test(a),d1=a=>c1.test(a),m1=a=>!te(a)&&!ne(a),h1=a=>Na(a,Pv,Kv),te=a=>Qv.test(a),Tl=a=>Na(a,Fv,s1),Fu=a=>Na(a,b1,xe),uv=a=>Na(a,Jv,Kv),v1=a=>Na(a,$v,d1),ar=a=>Na(a,Wv,f1),ne=a=>Zv.test(a),ji=a=>Ra(a,Fv),p1=a=>Ra(a,x1),sv=a=>Ra(a,Jv),g1=a=>Ra(a,Pv),y1=a=>Ra(a,$v),ir=a=>Ra(a,Wv,!0),Na=(a,o,u)=>{const c=Qv.exec(a);return c?c[1]?o(c[1]):u(c[2]):!1},Ra=(a,o,u=!1)=>{const c=Zv.exec(a);return c?c[1]?o(c[1]):u:!1},Jv=a=>a==="position"||a==="percentage",$v=a=>a==="image"||a==="url",Pv=a=>a==="length"||a==="size"||a==="bg-size",Fv=a=>a==="length",b1=a=>a==="number",x1=a=>a==="family-name",Wv=a=>a==="shadow",S1=()=>{const a=Ie("color"),o=Ie("font"),u=Ie("text"),c=Ie("font-weight"),s=Ie("tracking"),d=Ie("leading"),h=Ie("breakpoint"),v=Ie("container"),y=Ie("spacing"),g=Ie("radius"),x=Ie("shadow"),w=Ie("inset-shadow"),R=Ie("text-shadow"),N=Ie("drop-shadow"),z=Ie("blur"),T=Ie("perspective"),D=Ie("aspect"),L=Ie("ease"),k=Ie("animate"),_=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Z=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],G=()=>[...Z(),ne,te],I=()=>["auto","hidden","clip","visible","scroll"],$=()=>["auto","contain","none"],K=()=>[ne,te,y],oe=()=>[pa,"full","auto",...K()],ve=()=>[Fn,"none","subgrid",ne,te],pe=()=>["auto",{span:["full",Fn,ne,te]},Fn,ne,te],se=()=>[Fn,"auto",ne,te],ge=()=>["auto","min","max","fr",ne,te],me=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],fe=()=>["start","end","center","stretch","center-safe","end-safe"],M=()=>["auto",...K()],Q=()=>[pa,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...K()],B=()=>[a,ne,te],ae=()=>[...Z(),sv,uv,{position:[ne,te]}],E=()=>["no-repeat",{repeat:["","x","y","space","round"]}],V=()=>["auto","cover","contain",g1,h1,{size:[ne,te]}],F=()=>[Pu,ji,Tl],J=()=>["","none","full",g,ne,te],W=()=>["",xe,ji,Tl],he=()=>["solid","dashed","dotted","double"],ie=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],P=()=>[xe,Pu,sv,uv],ce=()=>["","none",z,ne,te],_e=()=>["none",xe,ne,te],Ae=()=>["none",xe,ne,te],Ce=()=>[xe,ne,te],je=()=>[pa,"full",...K()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Tn],breakpoint:[Tn],color:[u1],container:[Tn],"drop-shadow":[Tn],ease:["in","out","in-out"],font:[m1],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Tn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Tn],shadow:[Tn],spacing:["px",xe],text:[Tn],"text-shadow":[Tn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",pa,te,ne,D]}],container:["container"],columns:[{columns:[xe,te,ne,v]}],"break-after":[{"break-after":_()}],"break-before":[{"break-before":_()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:G()}],overflow:[{overflow:I()}],"overflow-x":[{"overflow-x":I()}],"overflow-y":[{"overflow-y":I()}],overscroll:[{overscroll:$()}],"overscroll-x":[{"overscroll-x":$()}],"overscroll-y":[{"overscroll-y":$()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:oe()}],"inset-x":[{"inset-x":oe()}],"inset-y":[{"inset-y":oe()}],start:[{start:oe()}],end:[{end:oe()}],top:[{top:oe()}],right:[{right:oe()}],bottom:[{bottom:oe()}],left:[{left:oe()}],visibility:["visible","invisible","collapse"],z:[{z:[Fn,"auto",ne,te]}],basis:[{basis:[pa,"full","auto",v,...K()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[xe,pa,"auto","initial","none",te]}],grow:[{grow:["",xe,ne,te]}],shrink:[{shrink:["",xe,ne,te]}],order:[{order:[Fn,"first","last","none",ne,te]}],"grid-cols":[{"grid-cols":ve()}],"col-start-end":[{col:pe()}],"col-start":[{"col-start":se()}],"col-end":[{"col-end":se()}],"grid-rows":[{"grid-rows":ve()}],"row-start-end":[{row:pe()}],"row-start":[{"row-start":se()}],"row-end":[{"row-end":se()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ge()}],"auto-rows":[{"auto-rows":ge()}],gap:[{gap:K()}],"gap-x":[{"gap-x":K()}],"gap-y":[{"gap-y":K()}],"justify-content":[{justify:[...me(),"normal"]}],"justify-items":[{"justify-items":[...fe(),"normal"]}],"justify-self":[{"justify-self":["auto",...fe()]}],"align-content":[{content:["normal",...me()]}],"align-items":[{items:[...fe(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...fe(),{baseline:["","last"]}]}],"place-content":[{"place-content":me()}],"place-items":[{"place-items":[...fe(),"baseline"]}],"place-self":[{"place-self":["auto",...fe()]}],p:[{p:K()}],px:[{px:K()}],py:[{py:K()}],ps:[{ps:K()}],pe:[{pe:K()}],pt:[{pt:K()}],pr:[{pr:K()}],pb:[{pb:K()}],pl:[{pl:K()}],m:[{m:M()}],mx:[{mx:M()}],my:[{my:M()}],ms:[{ms:M()}],me:[{me:M()}],mt:[{mt:M()}],mr:[{mr:M()}],mb:[{mb:M()}],ml:[{ml:M()}],"space-x":[{"space-x":K()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":K()}],"space-y-reverse":["space-y-reverse"],size:[{size:Q()}],w:[{w:[v,"screen",...Q()]}],"min-w":[{"min-w":[v,"screen","none",...Q()]}],"max-w":[{"max-w":[v,"screen","none","prose",{screen:[h]},...Q()]}],h:[{h:["screen","lh",...Q()]}],"min-h":[{"min-h":["screen","lh","none",...Q()]}],"max-h":[{"max-h":["screen","lh",...Q()]}],"font-size":[{text:["base",u,ji,Tl]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[c,ne,Fu]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",Pu,te]}],"font-family":[{font:[p1,te,o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,ne,te]}],"line-clamp":[{"line-clamp":[xe,"none",ne,Fu]}],leading:[{leading:[d,...K()]}],"list-image":[{"list-image":["none",ne,te]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ne,te]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:B()}],"text-color":[{text:B()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...he(),"wavy"]}],"text-decoration-thickness":[{decoration:[xe,"from-font","auto",ne,Tl]}],"text-decoration-color":[{decoration:B()}],"underline-offset":[{"underline-offset":[xe,"auto",ne,te]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:K()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ne,te]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ne,te]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ae()}],"bg-repeat":[{bg:E()}],"bg-size":[{bg:V()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Fn,ne,te],radial:["",ne,te],conic:[Fn,ne,te]},y1,v1]}],"bg-color":[{bg:B()}],"gradient-from-pos":[{from:F()}],"gradient-via-pos":[{via:F()}],"gradient-to-pos":[{to:F()}],"gradient-from":[{from:B()}],"gradient-via":[{via:B()}],"gradient-to":[{to:B()}],rounded:[{rounded:J()}],"rounded-s":[{"rounded-s":J()}],"rounded-e":[{"rounded-e":J()}],"rounded-t":[{"rounded-t":J()}],"rounded-r":[{"rounded-r":J()}],"rounded-b":[{"rounded-b":J()}],"rounded-l":[{"rounded-l":J()}],"rounded-ss":[{"rounded-ss":J()}],"rounded-se":[{"rounded-se":J()}],"rounded-ee":[{"rounded-ee":J()}],"rounded-es":[{"rounded-es":J()}],"rounded-tl":[{"rounded-tl":J()}],"rounded-tr":[{"rounded-tr":J()}],"rounded-br":[{"rounded-br":J()}],"rounded-bl":[{"rounded-bl":J()}],"border-w":[{border:W()}],"border-w-x":[{"border-x":W()}],"border-w-y":[{"border-y":W()}],"border-w-s":[{"border-s":W()}],"border-w-e":[{"border-e":W()}],"border-w-t":[{"border-t":W()}],"border-w-r":[{"border-r":W()}],"border-w-b":[{"border-b":W()}],"border-w-l":[{"border-l":W()}],"divide-x":[{"divide-x":W()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":W()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...he(),"hidden","none"]}],"divide-style":[{divide:[...he(),"hidden","none"]}],"border-color":[{border:B()}],"border-color-x":[{"border-x":B()}],"border-color-y":[{"border-y":B()}],"border-color-s":[{"border-s":B()}],"border-color-e":[{"border-e":B()}],"border-color-t":[{"border-t":B()}],"border-color-r":[{"border-r":B()}],"border-color-b":[{"border-b":B()}],"border-color-l":[{"border-l":B()}],"divide-color":[{divide:B()}],"outline-style":[{outline:[...he(),"none","hidden"]}],"outline-offset":[{"outline-offset":[xe,ne,te]}],"outline-w":[{outline:["",xe,ji,Tl]}],"outline-color":[{outline:B()}],shadow:[{shadow:["","none",x,ir,ar]}],"shadow-color":[{shadow:B()}],"inset-shadow":[{"inset-shadow":["none",w,ir,ar]}],"inset-shadow-color":[{"inset-shadow":B()}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:B()}],"ring-offset-w":[{"ring-offset":[xe,Tl]}],"ring-offset-color":[{"ring-offset":B()}],"inset-ring-w":[{"inset-ring":W()}],"inset-ring-color":[{"inset-ring":B()}],"text-shadow":[{"text-shadow":["none",R,ir,ar]}],"text-shadow-color":[{"text-shadow":B()}],opacity:[{opacity:[xe,ne,te]}],"mix-blend":[{"mix-blend":[...ie(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ie()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[xe]}],"mask-image-linear-from-pos":[{"mask-linear-from":P()}],"mask-image-linear-to-pos":[{"mask-linear-to":P()}],"mask-image-linear-from-color":[{"mask-linear-from":B()}],"mask-image-linear-to-color":[{"mask-linear-to":B()}],"mask-image-t-from-pos":[{"mask-t-from":P()}],"mask-image-t-to-pos":[{"mask-t-to":P()}],"mask-image-t-from-color":[{"mask-t-from":B()}],"mask-image-t-to-color":[{"mask-t-to":B()}],"mask-image-r-from-pos":[{"mask-r-from":P()}],"mask-image-r-to-pos":[{"mask-r-to":P()}],"mask-image-r-from-color":[{"mask-r-from":B()}],"mask-image-r-to-color":[{"mask-r-to":B()}],"mask-image-b-from-pos":[{"mask-b-from":P()}],"mask-image-b-to-pos":[{"mask-b-to":P()}],"mask-image-b-from-color":[{"mask-b-from":B()}],"mask-image-b-to-color":[{"mask-b-to":B()}],"mask-image-l-from-pos":[{"mask-l-from":P()}],"mask-image-l-to-pos":[{"mask-l-to":P()}],"mask-image-l-from-color":[{"mask-l-from":B()}],"mask-image-l-to-color":[{"mask-l-to":B()}],"mask-image-x-from-pos":[{"mask-x-from":P()}],"mask-image-x-to-pos":[{"mask-x-to":P()}],"mask-image-x-from-color":[{"mask-x-from":B()}],"mask-image-x-to-color":[{"mask-x-to":B()}],"mask-image-y-from-pos":[{"mask-y-from":P()}],"mask-image-y-to-pos":[{"mask-y-to":P()}],"mask-image-y-from-color":[{"mask-y-from":B()}],"mask-image-y-to-color":[{"mask-y-to":B()}],"mask-image-radial":[{"mask-radial":[ne,te]}],"mask-image-radial-from-pos":[{"mask-radial-from":P()}],"mask-image-radial-to-pos":[{"mask-radial-to":P()}],"mask-image-radial-from-color":[{"mask-radial-from":B()}],"mask-image-radial-to-color":[{"mask-radial-to":B()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":Z()}],"mask-image-conic-pos":[{"mask-conic":[xe]}],"mask-image-conic-from-pos":[{"mask-conic-from":P()}],"mask-image-conic-to-pos":[{"mask-conic-to":P()}],"mask-image-conic-from-color":[{"mask-conic-from":B()}],"mask-image-conic-to-color":[{"mask-conic-to":B()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ae()}],"mask-repeat":[{mask:E()}],"mask-size":[{mask:V()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ne,te]}],filter:[{filter:["","none",ne,te]}],blur:[{blur:ce()}],brightness:[{brightness:[xe,ne,te]}],contrast:[{contrast:[xe,ne,te]}],"drop-shadow":[{"drop-shadow":["","none",N,ir,ar]}],"drop-shadow-color":[{"drop-shadow":B()}],grayscale:[{grayscale:["",xe,ne,te]}],"hue-rotate":[{"hue-rotate":[xe,ne,te]}],invert:[{invert:["",xe,ne,te]}],saturate:[{saturate:[xe,ne,te]}],sepia:[{sepia:["",xe,ne,te]}],"backdrop-filter":[{"backdrop-filter":["","none",ne,te]}],"backdrop-blur":[{"backdrop-blur":ce()}],"backdrop-brightness":[{"backdrop-brightness":[xe,ne,te]}],"backdrop-contrast":[{"backdrop-contrast":[xe,ne,te]}],"backdrop-grayscale":[{"backdrop-grayscale":["",xe,ne,te]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[xe,ne,te]}],"backdrop-invert":[{"backdrop-invert":["",xe,ne,te]}],"backdrop-opacity":[{"backdrop-opacity":[xe,ne,te]}],"backdrop-saturate":[{"backdrop-saturate":[xe,ne,te]}],"backdrop-sepia":[{"backdrop-sepia":["",xe,ne,te]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":K()}],"border-spacing-x":[{"border-spacing-x":K()}],"border-spacing-y":[{"border-spacing-y":K()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ne,te]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[xe,"initial",ne,te]}],ease:[{ease:["linear","initial",L,ne,te]}],delay:[{delay:[xe,ne,te]}],animate:[{animate:["none",k,ne,te]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[T,ne,te]}],"perspective-origin":[{"perspective-origin":G()}],rotate:[{rotate:_e()}],"rotate-x":[{"rotate-x":_e()}],"rotate-y":[{"rotate-y":_e()}],"rotate-z":[{"rotate-z":_e()}],scale:[{scale:Ae()}],"scale-x":[{"scale-x":Ae()}],"scale-y":[{"scale-y":Ae()}],"scale-z":[{"scale-z":Ae()}],"scale-3d":["scale-3d"],skew:[{skew:Ce()}],"skew-x":[{"skew-x":Ce()}],"skew-y":[{"skew-y":Ce()}],transform:[{transform:[ne,te,"","none","gpu","cpu"]}],"transform-origin":[{origin:G()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:je()}],"translate-x":[{"translate-x":je()}],"translate-y":[{"translate-y":je()}],"translate-z":[{"translate-z":je()}],"translate-none":["translate-none"],accent:[{accent:B()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:B()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ne,te]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":K()}],"scroll-mx":[{"scroll-mx":K()}],"scroll-my":[{"scroll-my":K()}],"scroll-ms":[{"scroll-ms":K()}],"scroll-me":[{"scroll-me":K()}],"scroll-mt":[{"scroll-mt":K()}],"scroll-mr":[{"scroll-mr":K()}],"scroll-mb":[{"scroll-mb":K()}],"scroll-ml":[{"scroll-ml":K()}],"scroll-p":[{"scroll-p":K()}],"scroll-px":[{"scroll-px":K()}],"scroll-py":[{"scroll-py":K()}],"scroll-ps":[{"scroll-ps":K()}],"scroll-pe":[{"scroll-pe":K()}],"scroll-pt":[{"scroll-pt":K()}],"scroll-pr":[{"scroll-pr":K()}],"scroll-pb":[{"scroll-pb":K()}],"scroll-pl":[{"scroll-pl":K()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ne,te]}],fill:[{fill:["none",...B()]}],"stroke-w":[{stroke:[xe,ji,Tl,Fu]}],stroke:[{stroke:["none",...B()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},w1=n1(S1);function Te(...a){return w1(Yv(a))}const E1=As("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),kt=b.forwardRef(({className:a,variant:o,size:u,asChild:c=!1,...s},d)=>{const h=c?Bx:"button";return m.jsx(h,{className:Te(E1({variant:o,size:u,className:a})),ref:d,...s})});kt.displayName="Button";function Re(a,o,{checkForDefaultPrevented:u=!0}={}){return function(s){if(a?.(s),u===!1||!s.defaultPrevented)return o?.(s)}}function T1(a,o){const u=b.createContext(o),c=d=>{const{children:h,...v}=d,y=b.useMemo(()=>v,Object.values(v));return m.jsx(u.Provider,{value:y,children:h})};c.displayName=a+"Provider";function s(d){const h=b.useContext(u);if(h)return h;if(o!==void 0)return o;throw new Error(`\`${d}\` must be used within \`${a}\``)}return[c,s]}function Ca(a,o=[]){let u=[];function c(d,h){const v=b.createContext(h),y=u.length;u=[...u,h];const g=w=>{const{scope:R,children:N,...z}=w,T=R?.[a]?.[y]||v,D=b.useMemo(()=>z,Object.values(z));return m.jsx(T.Provider,{value:D,children:N})};g.displayName=d+"Provider";function x(w,R){const N=R?.[a]?.[y]||v,z=b.useContext(N);if(z)return z;if(h!==void 0)return h;throw new Error(`\`${w}\` must be used within \`${d}\``)}return[g,x]}const s=()=>{const d=u.map(h=>b.createContext(h));return function(v){const y=v?.[a]||d;return b.useMemo(()=>({[`__scope${a}`]:{...v,[a]:y}}),[v,y])}};return s.scopeName=a,[c,A1(s,...o)]}function A1(...a){const o=a[0];if(a.length===1)return o;const u=()=>{const c=a.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(d){const h=c.reduce((v,{useScope:y,scopeName:g})=>{const w=y(d)[`__scope${g}`];return{...v,...w}},{});return b.useMemo(()=>({[`__scope${o.scopeName}`]:h}),[h])}};return u.scopeName=o.scopeName,u}function Iv(a){const o=a+"CollectionProvider",[u,c]=Ca(o),[s,d]=u(o,{collectionRef:{current:null},itemMap:new Map}),h=T=>{const{scope:D,children:L}=T,k=Wn.useRef(null),_=Wn.useRef(new Map).current;return m.jsx(s,{scope:D,itemMap:_,collectionRef:k,children:L})};h.displayName=o;const v=a+"CollectionSlot",y=Ta(v),g=Wn.forwardRef((T,D)=>{const{scope:L,children:k}=T,_=d(v,L),Z=Ze(D,_.collectionRef);return m.jsx(y,{ref:Z,children:k})});g.displayName=v;const x=a+"CollectionItemSlot",w="data-radix-collection-item",R=Ta(x),N=Wn.forwardRef((T,D)=>{const{scope:L,children:k,..._}=T,Z=Wn.useRef(null),G=Ze(D,Z),I=d(x,L);return Wn.useEffect(()=>(I.itemMap.set(Z,{ref:Z,..._}),()=>void I.itemMap.delete(Z))),m.jsx(R,{[w]:"",ref:G,children:k})});N.displayName=x;function z(T){const D=d(a+"CollectionConsumer",T);return Wn.useCallback(()=>{const k=D.collectionRef.current;if(!k)return[];const _=Array.from(k.querySelectorAll(`[${w}]`));return Array.from(D.itemMap.values()).sort((I,$)=>_.indexOf(I.ref.current)-_.indexOf($.ref.current))},[D.collectionRef,D.itemMap])}return[{Provider:h,Slot:g,ItemSlot:N},z,c]}var ft=globalThis?.document?b.useLayoutEffect:()=>{},N1=Hv[" useId ".trim().toString()]||(()=>{}),R1=0;function el(a){const[o,u]=b.useState(N1());return ft(()=>{u(c=>c??String(R1++))},[a]),o?`radix-${o}`:""}var ki=Bv();const C1=Uv(ki);var D1=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],De=D1.reduce((a,o)=>{const u=Ta(`Primitive.${o}`),c=b.forwardRef((s,d)=>{const{asChild:h,...v}=s,y=h?u:o;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),m.jsx(y,{...v,ref:d})});return c.displayName=`Primitive.${o}`,{...a,[o]:c}},{});function M1(a,o){a&&ki.flushSync(()=>a.dispatchEvent(o))}function tl(a){const o=b.useRef(a);return b.useEffect(()=>{o.current=a}),b.useMemo(()=>(...u)=>o.current?.(...u),[])}var O1=Hv[" useInsertionEffect ".trim().toString()]||ft;function Hi({prop:a,defaultProp:o,onChange:u=()=>{},caller:c}){const[s,d,h]=_1({defaultProp:o,onChange:u}),v=a!==void 0,y=v?a:s;{const x=b.useRef(a!==void 0);b.useEffect(()=>{const w=x.current;w!==v&&console.warn(`${c} is changing from ${w?"controlled":"uncontrolled"} to ${v?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),x.current=v},[v,c])}const g=b.useCallback(x=>{if(v){const w=j1(x)?x(a):x;w!==a&&h.current?.(w)}else d(x)},[v,a,d,h]);return[y,g]}function _1({defaultProp:a,onChange:o}){const[u,c]=b.useState(a),s=b.useRef(u),d=b.useRef(o);return O1(()=>{d.current=o},[o]),b.useEffect(()=>{s.current!==u&&(d.current?.(u),s.current=u)},[u,s]),[u,c,d]}function j1(a){return typeof a=="function"}var z1=b.createContext(void 0);function Rs(a){const o=b.useContext(z1);return a||o||"ltr"}var Wu="rovingFocusGroup.onEntryFocus",U1={bubbles:!1,cancelable:!0},qi="RovingFocusGroup",[fs,ep,H1]=Iv(qi),[B1,tp]=Ca(qi,[H1]),[L1,k1]=B1(qi),np=b.forwardRef((a,o)=>m.jsx(fs.Provider,{scope:a.__scopeRovingFocusGroup,children:m.jsx(fs.Slot,{scope:a.__scopeRovingFocusGroup,children:m.jsx(q1,{...a,ref:o})})}));np.displayName=qi;var q1=b.forwardRef((a,o)=>{const{__scopeRovingFocusGroup:u,orientation:c,loop:s=!1,dir:d,currentTabStopId:h,defaultCurrentTabStopId:v,onCurrentTabStopIdChange:y,onEntryFocus:g,preventScrollOnEntryFocus:x=!1,...w}=a,R=b.useRef(null),N=Ze(o,R),z=Rs(d),[T,D]=Hi({prop:h,defaultProp:v??null,onChange:y,caller:qi}),[L,k]=b.useState(!1),_=tl(g),Z=ep(u),G=b.useRef(!1),[I,$]=b.useState(0);return b.useEffect(()=>{const K=R.current;if(K)return K.addEventListener(Wu,_),()=>K.removeEventListener(Wu,_)},[_]),m.jsx(L1,{scope:u,orientation:c,dir:z,loop:s,currentTabStopId:T,onItemFocus:b.useCallback(K=>D(K),[D]),onItemShiftTab:b.useCallback(()=>k(!0),[]),onFocusableItemAdd:b.useCallback(()=>$(K=>K+1),[]),onFocusableItemRemove:b.useCallback(()=>$(K=>K-1),[]),children:m.jsx(De.div,{tabIndex:L||I===0?-1:0,"data-orientation":c,...w,ref:N,style:{outline:"none",...a.style},onMouseDown:Re(a.onMouseDown,()=>{G.current=!0}),onFocus:Re(a.onFocus,K=>{const oe=!G.current;if(K.target===K.currentTarget&&oe&&!L){const ve=new CustomEvent(Wu,U1);if(K.currentTarget.dispatchEvent(ve),!ve.defaultPrevented){const pe=Z().filter(M=>M.focusable),se=pe.find(M=>M.active),ge=pe.find(M=>M.id===T),fe=[se,ge,...pe].filter(Boolean).map(M=>M.ref.current);ip(fe,x)}}G.current=!1}),onBlur:Re(a.onBlur,()=>k(!1))})})}),lp="RovingFocusGroupItem",ap=b.forwardRef((a,o)=>{const{__scopeRovingFocusGroup:u,focusable:c=!0,active:s=!1,tabStopId:d,children:h,...v}=a,y=el(),g=d||y,x=k1(lp,u),w=x.currentTabStopId===g,R=ep(u),{onFocusableItemAdd:N,onFocusableItemRemove:z,currentTabStopId:T}=x;return b.useEffect(()=>{if(c)return N(),()=>z()},[c,N,z]),m.jsx(fs.ItemSlot,{scope:u,id:g,focusable:c,active:s,children:m.jsx(De.span,{tabIndex:w?0:-1,"data-orientation":x.orientation,...v,ref:o,onMouseDown:Re(a.onMouseDown,D=>{c?x.onItemFocus(g):D.preventDefault()}),onFocus:Re(a.onFocus,()=>x.onItemFocus(g)),onKeyDown:Re(a.onKeyDown,D=>{if(D.key==="Tab"&&D.shiftKey){x.onItemShiftTab();return}if(D.target!==D.currentTarget)return;const L=V1(D,x.orientation,x.dir);if(L!==void 0){if(D.metaKey||D.ctrlKey||D.altKey||D.shiftKey)return;D.preventDefault();let _=R().filter(Z=>Z.focusable).map(Z=>Z.ref.current);if(L==="last")_.reverse();else if(L==="prev"||L==="next"){L==="prev"&&_.reverse();const Z=_.indexOf(D.currentTarget);_=x.loop?X1(_,Z+1):_.slice(Z+1)}setTimeout(()=>ip(_))}}),children:typeof h=="function"?h({isCurrentTabStop:w,hasTabStop:T!=null}):h})})});ap.displayName=lp;var G1={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Y1(a,o){return o!=="rtl"?a:a==="ArrowLeft"?"ArrowRight":a==="ArrowRight"?"ArrowLeft":a}function V1(a,o,u){const c=Y1(a.key,u);if(!(o==="vertical"&&["ArrowLeft","ArrowRight"].includes(c))&&!(o==="horizontal"&&["ArrowUp","ArrowDown"].includes(c)))return G1[c]}function ip(a,o=!1){const u=document.activeElement;for(const c of a)if(c===u||(c.focus({preventScroll:o}),document.activeElement!==u))return}function X1(a,o){return a.map((u,c)=>a[(o+c)%a.length])}var Q1=np,Z1=ap;function K1(a,o){return b.useReducer((u,c)=>o[u][c]??u,a)}var Gi=a=>{const{present:o,children:u}=a,c=J1(o),s=typeof u=="function"?u({present:c.isPresent}):b.Children.only(u),d=Ze(c.ref,$1(s));return typeof u=="function"||c.isPresent?b.cloneElement(s,{ref:d}):null};Gi.displayName="Presence";function J1(a){const[o,u]=b.useState(),c=b.useRef(null),s=b.useRef(a),d=b.useRef("none"),h=a?"mounted":"unmounted",[v,y]=K1(h,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return b.useEffect(()=>{const g=or(c.current);d.current=v==="mounted"?g:"none"},[v]),ft(()=>{const g=c.current,x=s.current;if(x!==a){const R=d.current,N=or(g);a?y("MOUNT"):N==="none"||g?.display==="none"?y("UNMOUNT"):y(x&&R!==N?"ANIMATION_OUT":"UNMOUNT"),s.current=a}},[a,y]),ft(()=>{if(o){let g;const x=o.ownerDocument.defaultView??window,w=N=>{const T=or(c.current).includes(N.animationName);if(N.target===o&&T&&(y("ANIMATION_END"),!s.current)){const D=o.style.animationFillMode;o.style.animationFillMode="forwards",g=x.setTimeout(()=>{o.style.animationFillMode==="forwards"&&(o.style.animationFillMode=D)})}},R=N=>{N.target===o&&(d.current=or(c.current))};return o.addEventListener("animationstart",R),o.addEventListener("animationcancel",w),o.addEventListener("animationend",w),()=>{x.clearTimeout(g),o.removeEventListener("animationstart",R),o.removeEventListener("animationcancel",w),o.removeEventListener("animationend",w)}}else y("ANIMATION_END")},[o,y]),{isPresent:["mounted","unmountSuspended"].includes(v),ref:b.useCallback(g=>{c.current=g?getComputedStyle(g):null,u(g)},[])}}function or(a){return a?.animationName||"none"}function $1(a){let o=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,u=o&&"isReactWarning"in o&&o.isReactWarning;return u?a.ref:(o=Object.getOwnPropertyDescriptor(a,"ref")?.get,u=o&&"isReactWarning"in o&&o.isReactWarning,u?a.props.ref:a.props.ref||a.ref)}var Er="Tabs",[P1,jE]=Ca(Er,[tp]),op=tp(),[F1,Cs]=P1(Er),rp=b.forwardRef((a,o)=>{const{__scopeTabs:u,value:c,onValueChange:s,defaultValue:d,orientation:h="horizontal",dir:v,activationMode:y="automatic",...g}=a,x=Rs(v),[w,R]=Hi({prop:c,onChange:s,defaultProp:d??"",caller:Er});return m.jsx(F1,{scope:u,baseId:el(),value:w,onValueChange:R,orientation:h,dir:x,activationMode:y,children:m.jsx(De.div,{dir:x,"data-orientation":h,...g,ref:o})})});rp.displayName=Er;var cp="TabsList",up=b.forwardRef((a,o)=>{const{__scopeTabs:u,loop:c=!0,...s}=a,d=Cs(cp,u),h=op(u);return m.jsx(Q1,{asChild:!0,...h,orientation:d.orientation,dir:d.dir,loop:c,children:m.jsx(De.div,{role:"tablist","aria-orientation":d.orientation,...s,ref:o})})});up.displayName=cp;var sp="TabsTrigger",fp=b.forwardRef((a,o)=>{const{__scopeTabs:u,value:c,disabled:s=!1,...d}=a,h=Cs(sp,u),v=op(u),y=hp(h.baseId,c),g=vp(h.baseId,c),x=c===h.value;return m.jsx(Z1,{asChild:!0,...v,focusable:!s,active:x,children:m.jsx(De.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":g,"data-state":x?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:y,...d,ref:o,onMouseDown:Re(a.onMouseDown,w=>{!s&&w.button===0&&w.ctrlKey===!1?h.onValueChange(c):w.preventDefault()}),onKeyDown:Re(a.onKeyDown,w=>{[" ","Enter"].includes(w.key)&&h.onValueChange(c)}),onFocus:Re(a.onFocus,()=>{const w=h.activationMode!=="manual";!x&&!s&&w&&h.onValueChange(c)})})})});fp.displayName=sp;var dp="TabsContent",mp=b.forwardRef((a,o)=>{const{__scopeTabs:u,value:c,forceMount:s,children:d,...h}=a,v=Cs(dp,u),y=hp(v.baseId,c),g=vp(v.baseId,c),x=c===v.value,w=b.useRef(x);return b.useEffect(()=>{const R=requestAnimationFrame(()=>w.current=!1);return()=>cancelAnimationFrame(R)},[]),m.jsx(Gi,{present:s||x,children:({present:R})=>m.jsx(De.div,{"data-state":x?"active":"inactive","data-orientation":v.orientation,role:"tabpanel","aria-labelledby":y,hidden:!R,id:g,tabIndex:0,...h,ref:o,style:{...a.style,animationDuration:w.current?"0s":void 0},children:R&&d})})});mp.displayName=dp;function hp(a,o){return`${a}-trigger-${o}`}function vp(a,o){return`${a}-content-${o}`}var W1=rp,pp=up,gp=fp,yp=mp;const I1=W1,bp=b.forwardRef(({className:a,...o},u)=>m.jsx(pp,{ref:u,className:Te("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...o}));bp.displayName=pp.displayName;const xa=b.forwardRef(({className:a,...o},u)=>m.jsx(gp,{ref:u,className:Te("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...o}));xa.displayName=gp.displayName;const eS=b.forwardRef(({className:a,...o},u)=>m.jsx(yp,{ref:u,className:Te("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...o}));eS.displayName=yp.displayName;function tS({children:a,activeTab:o,onTabChange:u}){return m.jsxs("div",{className:"min-h-screen bg-gray-50",children:[m.jsx("header",{className:"bg-white border-b border-gray-200 px-6 py-4",children:m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsxs("div",{className:"flex items-center gap-3",children:[m.jsx("div",{className:"p-2 bg-blue-600 rounded-lg",children:m.jsx(ev,{className:"w-6 h-6 text-white"})}),m.jsxs("div",{children:[m.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"Business Compliance"}),m.jsx("p",{className:"text-sm text-gray-600",children:"Management System"})]})]}),m.jsxs("div",{className:"flex items-center gap-3",children:[m.jsx(kt,{variant:"ghost",size:"icon",children:m.jsx($b,{className:"w-5 h-5"})}),m.jsx(kt,{variant:"ghost",size:"icon",children:m.jsx(Nx,{className:"w-5 h-5"})}),m.jsx(kt,{variant:"ghost",size:"icon",children:m.jsx(hx,{className:"w-5 h-5"})})]})]})}),m.jsx("nav",{className:"bg-white border-b border-gray-200 px-6",children:m.jsx(I1,{value:o,onValueChange:u,className:"w-full",children:m.jsxs(bp,{className:"grid w-full grid-cols-5 bg-transparent h-auto p-0",children:[m.jsxs(xa,{value:"overview",className:"flex items-center gap-2 px-4 py-3 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none",children:[m.jsx(ev,{className:"w-4 h-4"}),"Overview"]}),m.jsxs(xa,{value:"directors",className:"flex items-center gap-2 px-4 py-3 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none",children:[m.jsx(zx,{className:"w-4 h-4"}),"Directors"]}),m.jsxs(xa,{value:"documents",className:"flex items-center gap-2 px-4 py-3 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none",children:[m.jsx(cx,{className:"w-4 h-4"}),"Documents"]}),m.jsxs(xa,{value:"compliance",className:"flex items-center gap-2 px-4 py-3 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none",children:[m.jsx(tv,{className:"w-4 h-4"}),"Compliance"]}),m.jsxs(xa,{value:"pending",className:"flex items-center gap-2 px-4 py-3 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600 rounded-none",children:[m.jsx(tv,{className:"w-4 h-4"}),"Pending Tasks"]})]})})}),m.jsx("main",{className:"p-6",children:a})]})}const Qt=b.forwardRef(({className:a,...o},u)=>m.jsx("div",{ref:u,className:Te("rounded-lg border bg-card text-card-foreground shadow-sm",a),...o}));Qt.displayName="Card";const Zt=b.forwardRef(({className:a,...o},u)=>m.jsx("div",{ref:u,className:Te("flex flex-col space-y-1.5 p-6",a),...o}));Zt.displayName="CardHeader";const Kt=b.forwardRef(({className:a,...o},u)=>m.jsx("h3",{ref:u,className:Te("text-2xl font-semibold leading-none tracking-tight",a),...o}));Kt.displayName="CardTitle";const nS=b.forwardRef(({className:a,...o},u)=>m.jsx("p",{ref:u,className:Te("text-sm text-muted-foreground",a),...o}));nS.displayName="CardDescription";const Jt=b.forwardRef(({className:a,...o},u)=>m.jsx("div",{ref:u,className:Te("p-6 pt-0",a),...o}));Jt.displayName="CardContent";const lS=b.forwardRef(({className:a,...o},u)=>m.jsx("div",{ref:u,className:Te("flex items-center p-6 pt-0",a),...o}));lS.displayName="CardFooter";const aS=As("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-100/80",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-100/80",error:"border-transparent bg-red-100 text-red-800 hover:bg-red-100/80"}},defaultVariants:{variant:"default"}});function ds({className:a,variant:o,...u}){return m.jsx("div",{className:Te(aS({variant:o}),a),...u})}function fv({company:a,complianceItems:o}){const u=v=>{switch(v){case"Compliant":case"Filed":case"Active":return"success";case"Pending":return"warning";case"Overdue":return"error";default:return"secondary"}},c=v=>{switch(v){case"Compliant":case"Filed":case"Active":return m.jsx(nv,{className:"w-4 h-4"});case"Pending":return m.jsx(lv,{className:"w-4 h-4"});case"Overdue":return m.jsx(dr,{className:"w-4 h-4"});default:return m.jsx(dr,{className:"w-4 h-4"})}},s=o.filter(v=>v.status==="Overdue").length,d=o.filter(v=>v.status==="Pending").length,h=o.filter(v=>v.status==="Compliant").length;return m.jsxs("div",{className:"space-y-8",children:[m.jsxs(Qt,{children:[m.jsx(Zt,{className:"pb-6",children:m.jsxs("div",{className:"flex items-start justify-between",children:[m.jsxs("div",{children:[m.jsx(Kt,{className:"text-2xl mb-2",children:a.name}),m.jsxs("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[m.jsxs("div",{className:"flex items-center gap-2",children:[m.jsx(dx,{className:"w-4 h-4"}),m.jsx("span",{className:"font-mono",children:a.cin})]}),m.jsxs(ds,{variant:u(a.status),className:"flex items-center gap-1",children:[c(a.status),a.status]})]})]}),m.jsxs(kt,{className:"flex items-center gap-2",children:[m.jsx(ox,{className:"w-4 h-4"}),"Export Report"]})]})}),m.jsx(Jt,{children:m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[m.jsxs("div",{children:[m.jsx("h3",{className:"font-semibold text-foreground mb-3",children:"Company Details"}),m.jsxs("div",{className:"space-y-2 text-sm",children:[m.jsxs("div",{children:[m.jsx("span",{className:"text-muted-foreground",children:"Category:"})," ",m.jsx("span",{className:"font-medium",children:a.category})]}),m.jsxs("div",{children:[m.jsx("span",{className:"text-muted-foreground",children:"Sub-category:"})," ",m.jsx("span",{className:"font-medium",children:a.subcategory})]}),m.jsxs("div",{children:[m.jsx("span",{className:"text-muted-foreground",children:"Registration:"})," ",m.jsx("span",{className:"font-medium",children:a.registrationDate})]}),m.jsxs("div",{children:[m.jsx("span",{className:"text-muted-foreground",children:"ROC:"})," ",m.jsx("span",{className:"font-medium",children:a.registrarOffice})]})]})]}),m.jsxs("div",{children:[m.jsx("h3",{className:"font-semibold text-foreground mb-3",children:"Capital Structure"}),m.jsxs("div",{className:"space-y-2 text-sm",children:[m.jsxs("div",{children:[m.jsx("span",{className:"text-muted-foreground",children:"Authorized:"})," ",m.jsx("span",{className:"font-medium",children:a.authorizedCapital})]}),m.jsxs("div",{children:[m.jsx("span",{className:"text-muted-foreground",children:"Paid-up:"})," ",m.jsx("span",{className:"font-medium",children:a.paidupCapital})]})]})]}),m.jsxs("div",{children:[m.jsx("h3",{className:"font-semibold text-foreground mb-3",children:"Contact Information"}),m.jsxs("div",{className:"space-y-2 text-sm",children:[m.jsxs("div",{className:"flex items-center gap-2",children:[m.jsx(wx,{className:"w-3 h-3 text-muted-foreground"}),m.jsx("span",{children:a.contact.phone})]}),m.jsxs("div",{className:"flex items-center gap-2",children:[m.jsx(px,{className:"w-3 h-3 text-muted-foreground"}),m.jsx("span",{children:a.contact.email})]}),m.jsxs("div",{className:"flex items-center gap-2",children:[m.jsx(sx,{className:"w-3 h-3 text-muted-foreground"}),m.jsx("span",{children:a.contact.website})]}),m.jsxs("div",{className:"flex items-start gap-2",children:[m.jsx(yx,{className:"w-3 h-3 text-muted-foreground mt-0.5"}),m.jsxs("span",{children:[a.address.street,", ",a.address.city,", ",a.address.state," - ",a.address.pincode]})]})]})]})]})})]}),m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[m.jsxs(Qt,{children:[m.jsxs(Zt,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[m.jsx(Kt,{className:"text-sm font-medium",children:"Total Compliance Items"}),m.jsx(Mx,{className:"h-4 w-4 text-muted-foreground"})]}),m.jsxs(Jt,{children:[m.jsx("div",{className:"text-2xl font-bold",children:o.length}),m.jsx("p",{className:"text-xs text-muted-foreground",children:"Active compliance requirements"})]})]}),m.jsxs(Qt,{children:[m.jsxs(Zt,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[m.jsx(Kt,{className:"text-sm font-medium",children:"Compliant"}),m.jsx(nv,{className:"h-4 w-4 text-green-600"})]}),m.jsxs(Jt,{children:[m.jsx("div",{className:"text-2xl font-bold text-green-600",children:h}),m.jsx("p",{className:"text-xs text-muted-foreground",children:"Successfully completed"})]})]}),m.jsxs(Qt,{children:[m.jsxs(Zt,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[m.jsx(Kt,{className:"text-sm font-medium",children:"Pending"}),m.jsx(lv,{className:"h-4 w-4 text-yellow-600"})]}),m.jsxs(Jt,{children:[m.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:d}),m.jsx("p",{className:"text-xs text-muted-foreground",children:"Awaiting completion"})]})]}),m.jsxs(Qt,{children:[m.jsxs(Zt,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[m.jsx(Kt,{className:"text-sm font-medium",children:"Overdue"}),m.jsx(dr,{className:"h-4 w-4 text-red-600"})]}),m.jsxs(Jt,{children:[m.jsx("div",{className:"text-2xl font-bold text-red-600",children:s}),m.jsx("p",{className:"text-xs text-muted-foreground",children:"Require immediate attention"})]})]})]}),m.jsxs(Qt,{children:[m.jsx(Zt,{children:m.jsx(Kt,{children:"Recent Compliance Activities"})}),m.jsx(Jt,{children:m.jsx("div",{className:"space-y-4",children:o.slice(0,5).map(v=>m.jsxs("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[m.jsxs("div",{className:"flex items-center gap-3",children:[c(v.status),m.jsxs("div",{children:[m.jsx("p",{className:"font-medium",children:v.name}),m.jsx("p",{className:"text-sm text-muted-foreground",children:v.description})]})]}),m.jsxs("div",{className:"text-right",children:[m.jsx(ds,{variant:u(v.status),children:v.status}),m.jsxs("p",{className:"text-sm text-muted-foreground mt-1",children:["Due: ",v.dueDate]})]})]},v.id))})})]})]})}const xp=b.forwardRef(({className:a,...o},u)=>m.jsx("div",{className:"relative w-full overflow-auto",children:m.jsx("table",{ref:u,className:Te("w-full caption-bottom text-sm",a),...o})}));xp.displayName="Table";const Sp=b.forwardRef(({className:a,...o},u)=>m.jsx("thead",{ref:u,className:Te("[&_tr]:border-b",a),...o}));Sp.displayName="TableHeader";const wp=b.forwardRef(({className:a,...o},u)=>m.jsx("tbody",{ref:u,className:Te("[&_tr:last-child]:border-0",a),...o}));wp.displayName="TableBody";const iS=b.forwardRef(({className:a,...o},u)=>m.jsx("tfoot",{ref:u,className:Te("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...o}));iS.displayName="TableFooter";const ms=b.forwardRef(({className:a,...o},u)=>m.jsx("tr",{ref:u,className:Te("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...o}));ms.displayName="TableRow";const Al=b.forwardRef(({className:a,...o},u)=>m.jsx("th",{ref:u,className:Te("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...o}));Al.displayName="TableHead";const Nl=b.forwardRef(({className:a,...o},u)=>m.jsx("td",{ref:u,className:Te("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...o}));Nl.displayName="TableCell";const oS=b.forwardRef(({className:a,...o},u)=>m.jsx("caption",{ref:u,className:Te("mt-4 text-sm text-muted-foreground",a),...o}));oS.displayName="TableCaption";function rS(a,o=globalThis?.document){const u=tl(a);b.useEffect(()=>{const c=s=>{s.key==="Escape"&&u(s)};return o.addEventListener("keydown",c,{capture:!0}),()=>o.removeEventListener("keydown",c,{capture:!0})},[u,o])}var cS="DismissableLayer",hs="dismissableLayer.update",uS="dismissableLayer.pointerDownOutside",sS="dismissableLayer.focusOutside",dv,Ep=b.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Ds=b.forwardRef((a,o)=>{const{disableOutsidePointerEvents:u=!1,onEscapeKeyDown:c,onPointerDownOutside:s,onFocusOutside:d,onInteractOutside:h,onDismiss:v,...y}=a,g=b.useContext(Ep),[x,w]=b.useState(null),R=x?.ownerDocument??globalThis?.document,[,N]=b.useState({}),z=Ze(o,$=>w($)),T=Array.from(g.layers),[D]=[...g.layersWithOutsidePointerEventsDisabled].slice(-1),L=T.indexOf(D),k=x?T.indexOf(x):-1,_=g.layersWithOutsidePointerEventsDisabled.size>0,Z=k>=L,G=mS($=>{const K=$.target,oe=[...g.branches].some(ve=>ve.contains(K));!Z||oe||(s?.($),h?.($),$.defaultPrevented||v?.())},R),I=hS($=>{const K=$.target;[...g.branches].some(ve=>ve.contains(K))||(d?.($),h?.($),$.defaultPrevented||v?.())},R);return rS($=>{k===g.layers.size-1&&(c?.($),!$.defaultPrevented&&v&&($.preventDefault(),v()))},R),b.useEffect(()=>{if(x)return u&&(g.layersWithOutsidePointerEventsDisabled.size===0&&(dv=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),g.layersWithOutsidePointerEventsDisabled.add(x)),g.layers.add(x),mv(),()=>{u&&g.layersWithOutsidePointerEventsDisabled.size===1&&(R.body.style.pointerEvents=dv)}},[x,R,u,g]),b.useEffect(()=>()=>{x&&(g.layers.delete(x),g.layersWithOutsidePointerEventsDisabled.delete(x),mv())},[x,g]),b.useEffect(()=>{const $=()=>N({});return document.addEventListener(hs,$),()=>document.removeEventListener(hs,$)},[]),m.jsx(De.div,{...y,ref:z,style:{pointerEvents:_?Z?"auto":"none":void 0,...a.style},onFocusCapture:Re(a.onFocusCapture,I.onFocusCapture),onBlurCapture:Re(a.onBlurCapture,I.onBlurCapture),onPointerDownCapture:Re(a.onPointerDownCapture,G.onPointerDownCapture)})});Ds.displayName=cS;var fS="DismissableLayerBranch",dS=b.forwardRef((a,o)=>{const u=b.useContext(Ep),c=b.useRef(null),s=Ze(o,c);return b.useEffect(()=>{const d=c.current;if(d)return u.branches.add(d),()=>{u.branches.delete(d)}},[u.branches]),m.jsx(De.div,{...a,ref:s})});dS.displayName=fS;function mS(a,o=globalThis?.document){const u=tl(a),c=b.useRef(!1),s=b.useRef(()=>{});return b.useEffect(()=>{const d=v=>{if(v.target&&!c.current){let y=function(){Tp(uS,u,g,{discrete:!0})};const g={originalEvent:v};v.pointerType==="touch"?(o.removeEventListener("click",s.current),s.current=y,o.addEventListener("click",s.current,{once:!0})):y()}else o.removeEventListener("click",s.current);c.current=!1},h=window.setTimeout(()=>{o.addEventListener("pointerdown",d)},0);return()=>{window.clearTimeout(h),o.removeEventListener("pointerdown",d),o.removeEventListener("click",s.current)}},[o,u]),{onPointerDownCapture:()=>c.current=!0}}function hS(a,o=globalThis?.document){const u=tl(a),c=b.useRef(!1);return b.useEffect(()=>{const s=d=>{d.target&&!c.current&&Tp(sS,u,{originalEvent:d},{discrete:!1})};return o.addEventListener("focusin",s),()=>o.removeEventListener("focusin",s)},[o,u]),{onFocusCapture:()=>c.current=!0,onBlurCapture:()=>c.current=!1}}function mv(){const a=new CustomEvent(hs);document.dispatchEvent(a)}function Tp(a,o,u,{discrete:c}){const s=u.originalEvent.target,d=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:u});o&&s.addEventListener(a,o,{once:!0}),c?M1(s,d):s.dispatchEvent(d)}var Iu="focusScope.autoFocusOnMount",es="focusScope.autoFocusOnUnmount",hv={bubbles:!1,cancelable:!0},vS="FocusScope",Ms=b.forwardRef((a,o)=>{const{loop:u=!1,trapped:c=!1,onMountAutoFocus:s,onUnmountAutoFocus:d,...h}=a,[v,y]=b.useState(null),g=tl(s),x=tl(d),w=b.useRef(null),R=Ze(o,T=>y(T)),N=b.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;b.useEffect(()=>{if(c){let T=function(_){if(N.paused||!v)return;const Z=_.target;v.contains(Z)?w.current=Z:In(w.current,{select:!0})},D=function(_){if(N.paused||!v)return;const Z=_.relatedTarget;Z!==null&&(v.contains(Z)||In(w.current,{select:!0}))},L=function(_){if(document.activeElement===document.body)for(const G of _)G.removedNodes.length>0&&In(v)};document.addEventListener("focusin",T),document.addEventListener("focusout",D);const k=new MutationObserver(L);return v&&k.observe(v,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",T),document.removeEventListener("focusout",D),k.disconnect()}}},[c,v,N.paused]),b.useEffect(()=>{if(v){pv.add(N);const T=document.activeElement;if(!v.contains(T)){const L=new CustomEvent(Iu,hv);v.addEventListener(Iu,g),v.dispatchEvent(L),L.defaultPrevented||(pS(SS(Ap(v)),{select:!0}),document.activeElement===T&&In(v))}return()=>{v.removeEventListener(Iu,g),setTimeout(()=>{const L=new CustomEvent(es,hv);v.addEventListener(es,x),v.dispatchEvent(L),L.defaultPrevented||In(T??document.body,{select:!0}),v.removeEventListener(es,x),pv.remove(N)},0)}}},[v,g,x,N]);const z=b.useCallback(T=>{if(!u&&!c||N.paused)return;const D=T.key==="Tab"&&!T.altKey&&!T.ctrlKey&&!T.metaKey,L=document.activeElement;if(D&&L){const k=T.currentTarget,[_,Z]=gS(k);_&&Z?!T.shiftKey&&L===Z?(T.preventDefault(),u&&In(_,{select:!0})):T.shiftKey&&L===_&&(T.preventDefault(),u&&In(Z,{select:!0})):L===k&&T.preventDefault()}},[u,c,N.paused]);return m.jsx(De.div,{tabIndex:-1,...h,ref:R,onKeyDown:z})});Ms.displayName=vS;function pS(a,{select:o=!1}={}){const u=document.activeElement;for(const c of a)if(In(c,{select:o}),document.activeElement!==u)return}function gS(a){const o=Ap(a),u=vv(o,a),c=vv(o.reverse(),a);return[u,c]}function Ap(a){const o=[],u=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:c=>{const s=c.tagName==="INPUT"&&c.type==="hidden";return c.disabled||c.hidden||s?NodeFilter.FILTER_SKIP:c.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;u.nextNode();)o.push(u.currentNode);return o}function vv(a,o){for(const u of a)if(!yS(u,{upTo:o}))return u}function yS(a,{upTo:o}){if(getComputedStyle(a).visibility==="hidden")return!0;for(;a;){if(o!==void 0&&a===o)return!1;if(getComputedStyle(a).display==="none")return!0;a=a.parentElement}return!1}function bS(a){return a instanceof HTMLInputElement&&"select"in a}function In(a,{select:o=!1}={}){if(a&&a.focus){const u=document.activeElement;a.focus({preventScroll:!0}),a!==u&&bS(a)&&o&&a.select()}}var pv=xS();function xS(){let a=[];return{add(o){const u=a[0];o!==u&&u?.pause(),a=gv(a,o),a.unshift(o)},remove(o){a=gv(a,o),a[0]?.resume()}}}function gv(a,o){const u=[...a],c=u.indexOf(o);return c!==-1&&u.splice(c,1),u}function SS(a){return a.filter(o=>o.tagName!=="A")}var wS="Portal",Os=b.forwardRef((a,o)=>{const{container:u,...c}=a,[s,d]=b.useState(!1);ft(()=>d(!0),[]);const h=u||s&&globalThis?.document?.body;return h?C1.createPortal(m.jsx(De.div,{...c,ref:o}),h):null});Os.displayName=wS;var ts=0;function Np(){b.useEffect(()=>{const a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??yv()),document.body.insertAdjacentElement("beforeend",a[1]??yv()),ts++,()=>{ts===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(o=>o.remove()),ts--}},[])}function yv(){const a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var ln=function(){return ln=Object.assign||function(o){for(var u,c=1,s=arguments.length;c<s;c++){u=arguments[c];for(var d in u)Object.prototype.hasOwnProperty.call(u,d)&&(o[d]=u[d])}return o},ln.apply(this,arguments)};function Rp(a,o){var u={};for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&o.indexOf(c)<0&&(u[c]=a[c]);if(a!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,c=Object.getOwnPropertySymbols(a);s<c.length;s++)o.indexOf(c[s])<0&&Object.prototype.propertyIsEnumerable.call(a,c[s])&&(u[c[s]]=a[c[s]]);return u}function ES(a,o,u){if(u||arguments.length===2)for(var c=0,s=o.length,d;c<s;c++)(d||!(c in o))&&(d||(d=Array.prototype.slice.call(o,0,c)),d[c]=o[c]);return a.concat(d||Array.prototype.slice.call(o))}var mr="right-scroll-bar-position",hr="width-before-scroll-bar",TS="with-scroll-bars-hidden",AS="--removed-body-scroll-bar-size";function ns(a,o){return typeof a=="function"?a(o):a&&(a.current=o),a}function NS(a,o){var u=b.useState(function(){return{value:a,callback:o,facade:{get current(){return u.value},set current(c){var s=u.value;s!==c&&(u.value=c,u.callback(c,s))}}}})[0];return u.callback=o,u.facade}var RS=typeof window<"u"?b.useLayoutEffect:b.useEffect,bv=new WeakMap;function CS(a,o){var u=NS(null,function(c){return a.forEach(function(s){return ns(s,c)})});return RS(function(){var c=bv.get(u);if(c){var s=new Set(c),d=new Set(a),h=u.current;s.forEach(function(v){d.has(v)||ns(v,null)}),d.forEach(function(v){s.has(v)||ns(v,h)})}bv.set(u,a)},[a]),u}function DS(a){return a}function MS(a,o){o===void 0&&(o=DS);var u=[],c=!1,s={read:function(){if(c)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return u.length?u[u.length-1]:a},useMedium:function(d){var h=o(d,c);return u.push(h),function(){u=u.filter(function(v){return v!==h})}},assignSyncMedium:function(d){for(c=!0;u.length;){var h=u;u=[],h.forEach(d)}u={push:function(v){return d(v)},filter:function(){return u}}},assignMedium:function(d){c=!0;var h=[];if(u.length){var v=u;u=[],v.forEach(d),h=u}var y=function(){var x=h;h=[],x.forEach(d)},g=function(){return Promise.resolve().then(y)};g(),u={push:function(x){h.push(x),g()},filter:function(x){return h=h.filter(x),u}}}};return s}function OS(a){a===void 0&&(a={});var o=MS(null);return o.options=ln({async:!0,ssr:!1},a),o}var Cp=function(a){var o=a.sideCar,u=Rp(a,["sideCar"]);if(!o)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var c=o.read();if(!c)throw new Error("Sidecar medium not found");return b.createElement(c,ln({},u))};Cp.isSideCarExport=!0;function _S(a,o){return a.useMedium(o),Cp}var Dp=OS(),ls=function(){},Tr=b.forwardRef(function(a,o){var u=b.useRef(null),c=b.useState({onScrollCapture:ls,onWheelCapture:ls,onTouchMoveCapture:ls}),s=c[0],d=c[1],h=a.forwardProps,v=a.children,y=a.className,g=a.removeScrollBar,x=a.enabled,w=a.shards,R=a.sideCar,N=a.noRelative,z=a.noIsolation,T=a.inert,D=a.allowPinchZoom,L=a.as,k=L===void 0?"div":L,_=a.gapMode,Z=Rp(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),G=R,I=CS([u,o]),$=ln(ln({},Z),s);return b.createElement(b.Fragment,null,x&&b.createElement(G,{sideCar:Dp,removeScrollBar:g,shards:w,noRelative:N,noIsolation:z,inert:T,setCallbacks:d,allowPinchZoom:!!D,lockRef:u,gapMode:_}),h?b.cloneElement(b.Children.only(v),ln(ln({},$),{ref:I})):b.createElement(k,ln({},$,{className:y,ref:I}),v))});Tr.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Tr.classNames={fullWidth:hr,zeroRight:mr};var jS=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function zS(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var o=jS();return o&&a.setAttribute("nonce",o),a}function US(a,o){a.styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o))}function HS(a){var o=document.head||document.getElementsByTagName("head")[0];o.appendChild(a)}var BS=function(){var a=0,o=null;return{add:function(u){a==0&&(o=zS())&&(US(o,u),HS(o)),a++},remove:function(){a--,!a&&o&&(o.parentNode&&o.parentNode.removeChild(o),o=null)}}},LS=function(){var a=BS();return function(o,u){b.useEffect(function(){return a.add(o),function(){a.remove()}},[o&&u])}},Mp=function(){var a=LS(),o=function(u){var c=u.styles,s=u.dynamic;return a(c,s),null};return o},kS={left:0,top:0,right:0,gap:0},as=function(a){return parseInt(a||"",10)||0},qS=function(a){var o=window.getComputedStyle(document.body),u=o[a==="padding"?"paddingLeft":"marginLeft"],c=o[a==="padding"?"paddingTop":"marginTop"],s=o[a==="padding"?"paddingRight":"marginRight"];return[as(u),as(c),as(s)]},GS=function(a){if(a===void 0&&(a="margin"),typeof window>"u")return kS;var o=qS(a),u=document.documentElement.clientWidth,c=window.innerWidth;return{left:o[0],top:o[1],right:o[2],gap:Math.max(0,c-u+o[2]-o[0])}},YS=Mp(),wa="data-scroll-locked",VS=function(a,o,u,c){var s=a.left,d=a.top,h=a.right,v=a.gap;return u===void 0&&(u="margin"),`
  .`.concat(TS,` {
   overflow: hidden `).concat(c,`;
   padding-right: `).concat(v,"px ").concat(c,`;
  }
  body[`).concat(wa,`] {
    overflow: hidden `).concat(c,`;
    overscroll-behavior: contain;
    `).concat([o&&"position: relative ".concat(c,";"),u==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(d,`px;
    padding-right: `).concat(h,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(v,"px ").concat(c,`;
    `),u==="padding"&&"padding-right: ".concat(v,"px ").concat(c,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(mr,` {
    right: `).concat(v,"px ").concat(c,`;
  }
  
  .`).concat(hr,` {
    margin-right: `).concat(v,"px ").concat(c,`;
  }
  
  .`).concat(mr," .").concat(mr,` {
    right: 0 `).concat(c,`;
  }
  
  .`).concat(hr," .").concat(hr,` {
    margin-right: 0 `).concat(c,`;
  }
  
  body[`).concat(wa,`] {
    `).concat(AS,": ").concat(v,`px;
  }
`)},xv=function(){var a=parseInt(document.body.getAttribute(wa)||"0",10);return isFinite(a)?a:0},XS=function(){b.useEffect(function(){return document.body.setAttribute(wa,(xv()+1).toString()),function(){var a=xv()-1;a<=0?document.body.removeAttribute(wa):document.body.setAttribute(wa,a.toString())}},[])},QS=function(a){var o=a.noRelative,u=a.noImportant,c=a.gapMode,s=c===void 0?"margin":c;XS();var d=b.useMemo(function(){return GS(s)},[s]);return b.createElement(YS,{styles:VS(d,!o,s,u?"":"!important")})},vs=!1;if(typeof window<"u")try{var rr=Object.defineProperty({},"passive",{get:function(){return vs=!0,!0}});window.addEventListener("test",rr,rr),window.removeEventListener("test",rr,rr)}catch{vs=!1}var ga=vs?{passive:!1}:!1,ZS=function(a){return a.tagName==="TEXTAREA"},Op=function(a,o){if(!(a instanceof Element))return!1;var u=window.getComputedStyle(a);return u[o]!=="hidden"&&!(u.overflowY===u.overflowX&&!ZS(a)&&u[o]==="visible")},KS=function(a){return Op(a,"overflowY")},JS=function(a){return Op(a,"overflowX")},Sv=function(a,o){var u=o.ownerDocument,c=o;do{typeof ShadowRoot<"u"&&c instanceof ShadowRoot&&(c=c.host);var s=_p(a,c);if(s){var d=jp(a,c),h=d[1],v=d[2];if(h>v)return!0}c=c.parentNode}while(c&&c!==u.body);return!1},$S=function(a){var o=a.scrollTop,u=a.scrollHeight,c=a.clientHeight;return[o,u,c]},PS=function(a){var o=a.scrollLeft,u=a.scrollWidth,c=a.clientWidth;return[o,u,c]},_p=function(a,o){return a==="v"?KS(o):JS(o)},jp=function(a,o){return a==="v"?$S(o):PS(o)},FS=function(a,o){return a==="h"&&o==="rtl"?-1:1},WS=function(a,o,u,c,s){var d=FS(a,window.getComputedStyle(o).direction),h=d*c,v=u.target,y=o.contains(v),g=!1,x=h>0,w=0,R=0;do{if(!v)break;var N=jp(a,v),z=N[0],T=N[1],D=N[2],L=T-D-d*z;(z||L)&&_p(a,v)&&(w+=L,R+=z);var k=v.parentNode;v=k&&k.nodeType===Node.DOCUMENT_FRAGMENT_NODE?k.host:k}while(!y&&v!==document.body||y&&(o.contains(v)||o===v));return(x&&Math.abs(w)<1||!x&&Math.abs(R)<1)&&(g=!0),g},cr=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},wv=function(a){return[a.deltaX,a.deltaY]},Ev=function(a){return a&&"current"in a?a.current:a},IS=function(a,o){return a[0]===o[0]&&a[1]===o[1]},e2=function(a){return`
  .block-interactivity-`.concat(a,` {pointer-events: none;}
  .allow-interactivity-`).concat(a,` {pointer-events: all;}
`)},t2=0,ya=[];function n2(a){var o=b.useRef([]),u=b.useRef([0,0]),c=b.useRef(),s=b.useState(t2++)[0],d=b.useState(Mp)[0],h=b.useRef(a);b.useEffect(function(){h.current=a},[a]),b.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(s));var T=ES([a.lockRef.current],(a.shards||[]).map(Ev),!0).filter(Boolean);return T.forEach(function(D){return D.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),T.forEach(function(D){return D.classList.remove("allow-interactivity-".concat(s))})}}},[a.inert,a.lockRef.current,a.shards]);var v=b.useCallback(function(T,D){if("touches"in T&&T.touches.length===2||T.type==="wheel"&&T.ctrlKey)return!h.current.allowPinchZoom;var L=cr(T),k=u.current,_="deltaX"in T?T.deltaX:k[0]-L[0],Z="deltaY"in T?T.deltaY:k[1]-L[1],G,I=T.target,$=Math.abs(_)>Math.abs(Z)?"h":"v";if("touches"in T&&$==="h"&&I.type==="range")return!1;var K=Sv($,I);if(!K)return!0;if(K?G=$:(G=$==="v"?"h":"v",K=Sv($,I)),!K)return!1;if(!c.current&&"changedTouches"in T&&(_||Z)&&(c.current=G),!G)return!0;var oe=c.current||G;return WS(oe,D,T,oe==="h"?_:Z)},[]),y=b.useCallback(function(T){var D=T;if(!(!ya.length||ya[ya.length-1]!==d)){var L="deltaY"in D?wv(D):cr(D),k=o.current.filter(function(G){return G.name===D.type&&(G.target===D.target||D.target===G.shadowParent)&&IS(G.delta,L)})[0];if(k&&k.should){D.cancelable&&D.preventDefault();return}if(!k){var _=(h.current.shards||[]).map(Ev).filter(Boolean).filter(function(G){return G.contains(D.target)}),Z=_.length>0?v(D,_[0]):!h.current.noIsolation;Z&&D.cancelable&&D.preventDefault()}}},[]),g=b.useCallback(function(T,D,L,k){var _={name:T,delta:D,target:L,should:k,shadowParent:l2(L)};o.current.push(_),setTimeout(function(){o.current=o.current.filter(function(Z){return Z!==_})},1)},[]),x=b.useCallback(function(T){u.current=cr(T),c.current=void 0},[]),w=b.useCallback(function(T){g(T.type,wv(T),T.target,v(T,a.lockRef.current))},[]),R=b.useCallback(function(T){g(T.type,cr(T),T.target,v(T,a.lockRef.current))},[]);b.useEffect(function(){return ya.push(d),a.setCallbacks({onScrollCapture:w,onWheelCapture:w,onTouchMoveCapture:R}),document.addEventListener("wheel",y,ga),document.addEventListener("touchmove",y,ga),document.addEventListener("touchstart",x,ga),function(){ya=ya.filter(function(T){return T!==d}),document.removeEventListener("wheel",y,ga),document.removeEventListener("touchmove",y,ga),document.removeEventListener("touchstart",x,ga)}},[]);var N=a.removeScrollBar,z=a.inert;return b.createElement(b.Fragment,null,z?b.createElement(d,{styles:e2(s)}):null,N?b.createElement(QS,{noRelative:a.noRelative,gapMode:a.gapMode}):null)}function l2(a){for(var o=null;a!==null;)a instanceof ShadowRoot&&(o=a.host,a=a.host),a=a.parentNode;return o}const a2=_S(Dp,n2);var _s=b.forwardRef(function(a,o){return b.createElement(Tr,ln({},a,{ref:o,sideCar:a2}))});_s.classNames=Tr.classNames;var i2=function(a){if(typeof document>"u")return null;var o=Array.isArray(a)?a[0]:a;return o.ownerDocument.body},ba=new WeakMap,ur=new WeakMap,sr={},is=0,zp=function(a){return a&&(a.host||zp(a.parentNode))},o2=function(a,o){return o.map(function(u){if(a.contains(u))return u;var c=zp(u);return c&&a.contains(c)?c:(console.error("aria-hidden",u,"in not contained inside",a,". Doing nothing"),null)}).filter(function(u){return!!u})},r2=function(a,o,u,c){var s=o2(o,Array.isArray(a)?a:[a]);sr[u]||(sr[u]=new WeakMap);var d=sr[u],h=[],v=new Set,y=new Set(s),g=function(w){!w||v.has(w)||(v.add(w),g(w.parentNode))};s.forEach(g);var x=function(w){!w||y.has(w)||Array.prototype.forEach.call(w.children,function(R){if(v.has(R))x(R);else try{var N=R.getAttribute(c),z=N!==null&&N!=="false",T=(ba.get(R)||0)+1,D=(d.get(R)||0)+1;ba.set(R,T),d.set(R,D),h.push(R),T===1&&z&&ur.set(R,!0),D===1&&R.setAttribute(u,"true"),z||R.setAttribute(c,"true")}catch(L){console.error("aria-hidden: cannot operate on ",R,L)}})};return x(o),v.clear(),is++,function(){h.forEach(function(w){var R=ba.get(w)-1,N=d.get(w)-1;ba.set(w,R),d.set(w,N),R||(ur.has(w)||w.removeAttribute(c),ur.delete(w)),N||w.removeAttribute(u)}),is--,is||(ba=new WeakMap,ba=new WeakMap,ur=new WeakMap,sr={})}},Up=function(a,o,u){u===void 0&&(u="data-aria-hidden");var c=Array.from(Array.isArray(a)?a:[a]),s=i2(a);return s?(c.push.apply(c,Array.from(s.querySelectorAll("[aria-live], script"))),r2(c,s,u,"aria-hidden")):function(){return null}},Ar="Dialog",[Hp,zE]=Ca(Ar),[c2,Ft]=Hp(Ar),Bp=a=>{const{__scopeDialog:o,children:u,open:c,defaultOpen:s,onOpenChange:d,modal:h=!0}=a,v=b.useRef(null),y=b.useRef(null),[g,x]=Hi({prop:c,defaultProp:s??!1,onChange:d,caller:Ar});return m.jsx(c2,{scope:o,triggerRef:v,contentRef:y,contentId:el(),titleId:el(),descriptionId:el(),open:g,onOpenChange:x,onOpenToggle:b.useCallback(()=>x(w=>!w),[x]),modal:h,children:u})};Bp.displayName=Ar;var Lp="DialogTrigger",u2=b.forwardRef((a,o)=>{const{__scopeDialog:u,...c}=a,s=Ft(Lp,u),d=Ze(o,s.triggerRef);return m.jsx(De.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Us(s.open),...c,ref:d,onClick:Re(a.onClick,s.onOpenToggle)})});u2.displayName=Lp;var js="DialogPortal",[s2,kp]=Hp(js,{forceMount:void 0}),qp=a=>{const{__scopeDialog:o,forceMount:u,children:c,container:s}=a,d=Ft(js,o);return m.jsx(s2,{scope:o,forceMount:u,children:b.Children.map(c,h=>m.jsx(Gi,{present:u||d.open,children:m.jsx(Os,{asChild:!0,container:s,children:h})}))})};qp.displayName=js;var gr="DialogOverlay",Gp=b.forwardRef((a,o)=>{const u=kp(gr,a.__scopeDialog),{forceMount:c=u.forceMount,...s}=a,d=Ft(gr,a.__scopeDialog);return d.modal?m.jsx(Gi,{present:c||d.open,children:m.jsx(d2,{...s,ref:o})}):null});Gp.displayName=gr;var f2=Ta("DialogOverlay.RemoveScroll"),d2=b.forwardRef((a,o)=>{const{__scopeDialog:u,...c}=a,s=Ft(gr,u);return m.jsx(_s,{as:f2,allowPinchZoom:!0,shards:[s.contentRef],children:m.jsx(De.div,{"data-state":Us(s.open),...c,ref:o,style:{pointerEvents:"auto",...c.style}})})}),Rl="DialogContent",Yp=b.forwardRef((a,o)=>{const u=kp(Rl,a.__scopeDialog),{forceMount:c=u.forceMount,...s}=a,d=Ft(Rl,a.__scopeDialog);return m.jsx(Gi,{present:c||d.open,children:d.modal?m.jsx(m2,{...s,ref:o}):m.jsx(h2,{...s,ref:o})})});Yp.displayName=Rl;var m2=b.forwardRef((a,o)=>{const u=Ft(Rl,a.__scopeDialog),c=b.useRef(null),s=Ze(o,u.contentRef,c);return b.useEffect(()=>{const d=c.current;if(d)return Up(d)},[]),m.jsx(Vp,{...a,ref:s,trapFocus:u.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Re(a.onCloseAutoFocus,d=>{d.preventDefault(),u.triggerRef.current?.focus()}),onPointerDownOutside:Re(a.onPointerDownOutside,d=>{const h=d.detail.originalEvent,v=h.button===0&&h.ctrlKey===!0;(h.button===2||v)&&d.preventDefault()}),onFocusOutside:Re(a.onFocusOutside,d=>d.preventDefault())})}),h2=b.forwardRef((a,o)=>{const u=Ft(Rl,a.__scopeDialog),c=b.useRef(!1),s=b.useRef(!1);return m.jsx(Vp,{...a,ref:o,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:d=>{a.onCloseAutoFocus?.(d),d.defaultPrevented||(c.current||u.triggerRef.current?.focus(),d.preventDefault()),c.current=!1,s.current=!1},onInteractOutside:d=>{a.onInteractOutside?.(d),d.defaultPrevented||(c.current=!0,d.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const h=d.target;u.triggerRef.current?.contains(h)&&d.preventDefault(),d.detail.originalEvent.type==="focusin"&&s.current&&d.preventDefault()}})}),Vp=b.forwardRef((a,o)=>{const{__scopeDialog:u,trapFocus:c,onOpenAutoFocus:s,onCloseAutoFocus:d,...h}=a,v=Ft(Rl,u),y=b.useRef(null),g=Ze(o,y);return Np(),m.jsxs(m.Fragment,{children:[m.jsx(Ms,{asChild:!0,loop:!0,trapped:c,onMountAutoFocus:s,onUnmountAutoFocus:d,children:m.jsx(Ds,{role:"dialog",id:v.contentId,"aria-describedby":v.descriptionId,"aria-labelledby":v.titleId,"data-state":Us(v.open),...h,ref:g,onDismiss:()=>v.onOpenChange(!1)})}),m.jsxs(m.Fragment,{children:[m.jsx(v2,{titleId:v.titleId}),m.jsx(g2,{contentRef:y,descriptionId:v.descriptionId})]})]})}),zs="DialogTitle",Xp=b.forwardRef((a,o)=>{const{__scopeDialog:u,...c}=a,s=Ft(zs,u);return m.jsx(De.h2,{id:s.titleId,...c,ref:o})});Xp.displayName=zs;var Qp="DialogDescription",Zp=b.forwardRef((a,o)=>{const{__scopeDialog:u,...c}=a,s=Ft(Qp,u);return m.jsx(De.p,{id:s.descriptionId,...c,ref:o})});Zp.displayName=Qp;var Kp="DialogClose",Jp=b.forwardRef((a,o)=>{const{__scopeDialog:u,...c}=a,s=Ft(Kp,u);return m.jsx(De.button,{type:"button",...c,ref:o,onClick:Re(a.onClick,()=>s.onOpenChange(!1))})});Jp.displayName=Kp;function Us(a){return a?"open":"closed"}var $p="DialogTitleWarning",[UE,Pp]=T1($p,{contentName:Rl,titleName:zs,docsSlug:"dialog"}),v2=({titleId:a})=>{const o=Pp($p),u=`\`${o.contentName}\` requires a \`${o.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${o.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${o.docsSlug}`;return b.useEffect(()=>{a&&(document.getElementById(a)||console.error(u))},[u,a]),null},p2="DialogDescriptionWarning",g2=({contentRef:a,descriptionId:o})=>{const c=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Pp(p2).contentName}}.`;return b.useEffect(()=>{const s=a.current?.getAttribute("aria-describedby");o&&s&&(document.getElementById(o)||console.warn(c))},[c,a,o]),null},y2=Bp,b2=qp,Fp=Gp,Wp=Yp,Ip=Xp,eg=Zp,x2=Jp;const tg=y2,S2=b2,ng=b.forwardRef(({className:a,...o},u)=>m.jsx(Fp,{ref:u,className:Te("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...o}));ng.displayName=Fp.displayName;const Hs=b.forwardRef(({className:a,children:o,...u},c)=>m.jsxs(S2,{children:[m.jsx(ng,{}),m.jsxs(Wp,{ref:c,className:Te("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...u,children:[o,m.jsxs(x2,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[m.jsx(Hx,{className:"h-4 w-4"}),m.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Hs.displayName=Wp.displayName;const Bs=({className:a,...o})=>m.jsx("div",{className:Te("flex flex-col space-y-1.5 text-center sm:text-left",a),...o});Bs.displayName="DialogHeader";const Ls=({className:a,...o})=>m.jsx("div",{className:Te("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...o});Ls.displayName="DialogFooter";const ks=b.forwardRef(({className:a,...o},u)=>m.jsx(Ip,{ref:u,className:Te("text-lg font-semibold leading-none tracking-tight",a),...o}));ks.displayName=Ip.displayName;const w2=b.forwardRef(({className:a,...o},u)=>m.jsx(eg,{ref:u,className:Te("text-sm text-muted-foreground",a),...o}));w2.displayName=eg.displayName;const vr=b.forwardRef(({className:a,type:o,...u},c)=>m.jsx("input",{type:o,className:Te("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...u}));vr.displayName="Input";var E2="Label",lg=b.forwardRef((a,o)=>m.jsx(De.label,{...a,ref:o,onMouseDown:u=>{u.target.closest("button, input, select, textarea")||(a.onMouseDown?.(u),!u.defaultPrevented&&u.detail>1&&u.preventDefault())}}));lg.displayName=E2;var ag=lg;const T2=As("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),zi=b.forwardRef(({className:a,...o},u)=>m.jsx(ag,{ref:u,className:Te(T2(),a),...o}));zi.displayName=ag.displayName;function Tv(a,[o,u]){return Math.min(u,Math.max(o,a))}const A2=["top","right","bottom","left"],nl=Math.min,Ct=Math.max,yr=Math.round,fr=Math.floor,on=a=>({x:a,y:a}),N2={left:"right",right:"left",bottom:"top",top:"bottom"},R2={start:"end",end:"start"};function ps(a,o,u){return Ct(a,nl(o,u))}function An(a,o){return typeof a=="function"?a(o):a}function Nn(a){return a.split("-")[0]}function Da(a){return a.split("-")[1]}function qs(a){return a==="x"?"y":"x"}function Gs(a){return a==="y"?"height":"width"}const C2=new Set(["top","bottom"]);function an(a){return C2.has(Nn(a))?"y":"x"}function Ys(a){return qs(an(a))}function D2(a,o,u){u===void 0&&(u=!1);const c=Da(a),s=Ys(a),d=Gs(s);let h=s==="x"?c===(u?"end":"start")?"right":"left":c==="start"?"bottom":"top";return o.reference[d]>o.floating[d]&&(h=br(h)),[h,br(h)]}function M2(a){const o=br(a);return[gs(a),o,gs(o)]}function gs(a){return a.replace(/start|end/g,o=>R2[o])}const Av=["left","right"],Nv=["right","left"],O2=["top","bottom"],_2=["bottom","top"];function j2(a,o,u){switch(a){case"top":case"bottom":return u?o?Nv:Av:o?Av:Nv;case"left":case"right":return o?O2:_2;default:return[]}}function z2(a,o,u,c){const s=Da(a);let d=j2(Nn(a),u==="start",c);return s&&(d=d.map(h=>h+"-"+s),o&&(d=d.concat(d.map(gs)))),d}function br(a){return a.replace(/left|right|bottom|top/g,o=>N2[o])}function U2(a){return{top:0,right:0,bottom:0,left:0,...a}}function ig(a){return typeof a!="number"?U2(a):{top:a,right:a,bottom:a,left:a}}function xr(a){const{x:o,y:u,width:c,height:s}=a;return{width:c,height:s,top:u,left:o,right:o+c,bottom:u+s,x:o,y:u}}function Rv(a,o,u){let{reference:c,floating:s}=a;const d=an(o),h=Ys(o),v=Gs(h),y=Nn(o),g=d==="y",x=c.x+c.width/2-s.width/2,w=c.y+c.height/2-s.height/2,R=c[v]/2-s[v]/2;let N;switch(y){case"top":N={x,y:c.y-s.height};break;case"bottom":N={x,y:c.y+c.height};break;case"right":N={x:c.x+c.width,y:w};break;case"left":N={x:c.x-s.width,y:w};break;default:N={x:c.x,y:c.y}}switch(Da(o)){case"start":N[h]-=R*(u&&g?-1:1);break;case"end":N[h]+=R*(u&&g?-1:1);break}return N}const H2=async(a,o,u)=>{const{placement:c="bottom",strategy:s="absolute",middleware:d=[],platform:h}=u,v=d.filter(Boolean),y=await(h.isRTL==null?void 0:h.isRTL(o));let g=await h.getElementRects({reference:a,floating:o,strategy:s}),{x,y:w}=Rv(g,c,y),R=c,N={},z=0;for(let T=0;T<v.length;T++){const{name:D,fn:L}=v[T],{x:k,y:_,data:Z,reset:G}=await L({x,y:w,initialPlacement:c,placement:R,strategy:s,middlewareData:N,rects:g,platform:h,elements:{reference:a,floating:o}});x=k??x,w=_??w,N={...N,[D]:{...N[D],...Z}},G&&z<=50&&(z++,typeof G=="object"&&(G.placement&&(R=G.placement),G.rects&&(g=G.rects===!0?await h.getElementRects({reference:a,floating:o,strategy:s}):G.rects),{x,y:w}=Rv(g,R,y)),T=-1)}return{x,y:w,placement:R,strategy:s,middlewareData:N}};async function Bi(a,o){var u;o===void 0&&(o={});const{x:c,y:s,platform:d,rects:h,elements:v,strategy:y}=a,{boundary:g="clippingAncestors",rootBoundary:x="viewport",elementContext:w="floating",altBoundary:R=!1,padding:N=0}=An(o,a),z=ig(N),D=v[R?w==="floating"?"reference":"floating":w],L=xr(await d.getClippingRect({element:(u=await(d.isElement==null?void 0:d.isElement(D)))==null||u?D:D.contextElement||await(d.getDocumentElement==null?void 0:d.getDocumentElement(v.floating)),boundary:g,rootBoundary:x,strategy:y})),k=w==="floating"?{x:c,y:s,width:h.floating.width,height:h.floating.height}:h.reference,_=await(d.getOffsetParent==null?void 0:d.getOffsetParent(v.floating)),Z=await(d.isElement==null?void 0:d.isElement(_))?await(d.getScale==null?void 0:d.getScale(_))||{x:1,y:1}:{x:1,y:1},G=xr(d.convertOffsetParentRelativeRectToViewportRelativeRect?await d.convertOffsetParentRelativeRectToViewportRelativeRect({elements:v,rect:k,offsetParent:_,strategy:y}):k);return{top:(L.top-G.top+z.top)/Z.y,bottom:(G.bottom-L.bottom+z.bottom)/Z.y,left:(L.left-G.left+z.left)/Z.x,right:(G.right-L.right+z.right)/Z.x}}const B2=a=>({name:"arrow",options:a,async fn(o){const{x:u,y:c,placement:s,rects:d,platform:h,elements:v,middlewareData:y}=o,{element:g,padding:x=0}=An(a,o)||{};if(g==null)return{};const w=ig(x),R={x:u,y:c},N=Ys(s),z=Gs(N),T=await h.getDimensions(g),D=N==="y",L=D?"top":"left",k=D?"bottom":"right",_=D?"clientHeight":"clientWidth",Z=d.reference[z]+d.reference[N]-R[N]-d.floating[z],G=R[N]-d.reference[N],I=await(h.getOffsetParent==null?void 0:h.getOffsetParent(g));let $=I?I[_]:0;(!$||!await(h.isElement==null?void 0:h.isElement(I)))&&($=v.floating[_]||d.floating[z]);const K=Z/2-G/2,oe=$/2-T[z]/2-1,ve=nl(w[L],oe),pe=nl(w[k],oe),se=ve,ge=$-T[z]-pe,me=$/2-T[z]/2+K,fe=ps(se,me,ge),M=!y.arrow&&Da(s)!=null&&me!==fe&&d.reference[z]/2-(me<se?ve:pe)-T[z]/2<0,Q=M?me<se?me-se:me-ge:0;return{[N]:R[N]+Q,data:{[N]:fe,centerOffset:me-fe-Q,...M&&{alignmentOffset:Q}},reset:M}}}),L2=function(a){return a===void 0&&(a={}),{name:"flip",options:a,async fn(o){var u,c;const{placement:s,middlewareData:d,rects:h,initialPlacement:v,platform:y,elements:g}=o,{mainAxis:x=!0,crossAxis:w=!0,fallbackPlacements:R,fallbackStrategy:N="bestFit",fallbackAxisSideDirection:z="none",flipAlignment:T=!0,...D}=An(a,o);if((u=d.arrow)!=null&&u.alignmentOffset)return{};const L=Nn(s),k=an(v),_=Nn(v)===v,Z=await(y.isRTL==null?void 0:y.isRTL(g.floating)),G=R||(_||!T?[br(v)]:M2(v)),I=z!=="none";!R&&I&&G.push(...z2(v,T,z,Z));const $=[v,...G],K=await Bi(o,D),oe=[];let ve=((c=d.flip)==null?void 0:c.overflows)||[];if(x&&oe.push(K[L]),w){const me=D2(s,h,Z);oe.push(K[me[0]],K[me[1]])}if(ve=[...ve,{placement:s,overflows:oe}],!oe.every(me=>me<=0)){var pe,se;const me=(((pe=d.flip)==null?void 0:pe.index)||0)+1,fe=$[me];if(fe&&(!(w==="alignment"?k!==an(fe):!1)||ve.every(B=>B.overflows[0]>0&&an(B.placement)===k)))return{data:{index:me,overflows:ve},reset:{placement:fe}};let M=(se=ve.filter(Q=>Q.overflows[0]<=0).sort((Q,B)=>Q.overflows[1]-B.overflows[1])[0])==null?void 0:se.placement;if(!M)switch(N){case"bestFit":{var ge;const Q=(ge=ve.filter(B=>{if(I){const ae=an(B.placement);return ae===k||ae==="y"}return!0}).map(B=>[B.placement,B.overflows.filter(ae=>ae>0).reduce((ae,E)=>ae+E,0)]).sort((B,ae)=>B[1]-ae[1])[0])==null?void 0:ge[0];Q&&(M=Q);break}case"initialPlacement":M=v;break}if(s!==M)return{reset:{placement:M}}}return{}}}};function Cv(a,o){return{top:a.top-o.height,right:a.right-o.width,bottom:a.bottom-o.height,left:a.left-o.width}}function Dv(a){return A2.some(o=>a[o]>=0)}const k2=function(a){return a===void 0&&(a={}),{name:"hide",options:a,async fn(o){const{rects:u}=o,{strategy:c="referenceHidden",...s}=An(a,o);switch(c){case"referenceHidden":{const d=await Bi(o,{...s,elementContext:"reference"}),h=Cv(d,u.reference);return{data:{referenceHiddenOffsets:h,referenceHidden:Dv(h)}}}case"escaped":{const d=await Bi(o,{...s,altBoundary:!0}),h=Cv(d,u.floating);return{data:{escapedOffsets:h,escaped:Dv(h)}}}default:return{}}}}},og=new Set(["left","top"]);async function q2(a,o){const{placement:u,platform:c,elements:s}=a,d=await(c.isRTL==null?void 0:c.isRTL(s.floating)),h=Nn(u),v=Da(u),y=an(u)==="y",g=og.has(h)?-1:1,x=d&&y?-1:1,w=An(o,a);let{mainAxis:R,crossAxis:N,alignmentAxis:z}=typeof w=="number"?{mainAxis:w,crossAxis:0,alignmentAxis:null}:{mainAxis:w.mainAxis||0,crossAxis:w.crossAxis||0,alignmentAxis:w.alignmentAxis};return v&&typeof z=="number"&&(N=v==="end"?z*-1:z),y?{x:N*x,y:R*g}:{x:R*g,y:N*x}}const G2=function(a){return a===void 0&&(a=0),{name:"offset",options:a,async fn(o){var u,c;const{x:s,y:d,placement:h,middlewareData:v}=o,y=await q2(o,a);return h===((u=v.offset)==null?void 0:u.placement)&&(c=v.arrow)!=null&&c.alignmentOffset?{}:{x:s+y.x,y:d+y.y,data:{...y,placement:h}}}}},Y2=function(a){return a===void 0&&(a={}),{name:"shift",options:a,async fn(o){const{x:u,y:c,placement:s}=o,{mainAxis:d=!0,crossAxis:h=!1,limiter:v={fn:D=>{let{x:L,y:k}=D;return{x:L,y:k}}},...y}=An(a,o),g={x:u,y:c},x=await Bi(o,y),w=an(Nn(s)),R=qs(w);let N=g[R],z=g[w];if(d){const D=R==="y"?"top":"left",L=R==="y"?"bottom":"right",k=N+x[D],_=N-x[L];N=ps(k,N,_)}if(h){const D=w==="y"?"top":"left",L=w==="y"?"bottom":"right",k=z+x[D],_=z-x[L];z=ps(k,z,_)}const T=v.fn({...o,[R]:N,[w]:z});return{...T,data:{x:T.x-u,y:T.y-c,enabled:{[R]:d,[w]:h}}}}}},V2=function(a){return a===void 0&&(a={}),{options:a,fn(o){const{x:u,y:c,placement:s,rects:d,middlewareData:h}=o,{offset:v=0,mainAxis:y=!0,crossAxis:g=!0}=An(a,o),x={x:u,y:c},w=an(s),R=qs(w);let N=x[R],z=x[w];const T=An(v,o),D=typeof T=="number"?{mainAxis:T,crossAxis:0}:{mainAxis:0,crossAxis:0,...T};if(y){const _=R==="y"?"height":"width",Z=d.reference[R]-d.floating[_]+D.mainAxis,G=d.reference[R]+d.reference[_]-D.mainAxis;N<Z?N=Z:N>G&&(N=G)}if(g){var L,k;const _=R==="y"?"width":"height",Z=og.has(Nn(s)),G=d.reference[w]-d.floating[_]+(Z&&((L=h.offset)==null?void 0:L[w])||0)+(Z?0:D.crossAxis),I=d.reference[w]+d.reference[_]+(Z?0:((k=h.offset)==null?void 0:k[w])||0)-(Z?D.crossAxis:0);z<G?z=G:z>I&&(z=I)}return{[R]:N,[w]:z}}}},X2=function(a){return a===void 0&&(a={}),{name:"size",options:a,async fn(o){var u,c;const{placement:s,rects:d,platform:h,elements:v}=o,{apply:y=()=>{},...g}=An(a,o),x=await Bi(o,g),w=Nn(s),R=Da(s),N=an(s)==="y",{width:z,height:T}=d.floating;let D,L;w==="top"||w==="bottom"?(D=w,L=R===(await(h.isRTL==null?void 0:h.isRTL(v.floating))?"start":"end")?"left":"right"):(L=w,D=R==="end"?"top":"bottom");const k=T-x.top-x.bottom,_=z-x.left-x.right,Z=nl(T-x[D],k),G=nl(z-x[L],_),I=!o.middlewareData.shift;let $=Z,K=G;if((u=o.middlewareData.shift)!=null&&u.enabled.x&&(K=_),(c=o.middlewareData.shift)!=null&&c.enabled.y&&($=k),I&&!R){const ve=Ct(x.left,0),pe=Ct(x.right,0),se=Ct(x.top,0),ge=Ct(x.bottom,0);N?K=z-2*(ve!==0||pe!==0?ve+pe:Ct(x.left,x.right)):$=T-2*(se!==0||ge!==0?se+ge:Ct(x.top,x.bottom))}await y({...o,availableWidth:K,availableHeight:$});const oe=await h.getDimensions(v.floating);return z!==oe.width||T!==oe.height?{reset:{rects:!0}}:{}}}};function Nr(){return typeof window<"u"}function Ma(a){return rg(a)?(a.nodeName||"").toLowerCase():"#document"}function Dt(a){var o;return(a==null||(o=a.ownerDocument)==null?void 0:o.defaultView)||window}function cn(a){var o;return(o=(rg(a)?a.ownerDocument:a.document)||window.document)==null?void 0:o.documentElement}function rg(a){return Nr()?a instanceof Node||a instanceof Dt(a).Node:!1}function $t(a){return Nr()?a instanceof Element||a instanceof Dt(a).Element:!1}function rn(a){return Nr()?a instanceof HTMLElement||a instanceof Dt(a).HTMLElement:!1}function Mv(a){return!Nr()||typeof ShadowRoot>"u"?!1:a instanceof ShadowRoot||a instanceof Dt(a).ShadowRoot}const Q2=new Set(["inline","contents"]);function Yi(a){const{overflow:o,overflowX:u,overflowY:c,display:s}=Pt(a);return/auto|scroll|overlay|hidden|clip/.test(o+c+u)&&!Q2.has(s)}const Z2=new Set(["table","td","th"]);function K2(a){return Z2.has(Ma(a))}const J2=[":popover-open",":modal"];function Rr(a){return J2.some(o=>{try{return a.matches(o)}catch{return!1}})}const $2=["transform","translate","scale","rotate","perspective"],P2=["transform","translate","scale","rotate","perspective","filter"],F2=["paint","layout","strict","content"];function Vs(a){const o=Xs(),u=$t(a)?Pt(a):a;return $2.some(c=>u[c]?u[c]!=="none":!1)||(u.containerType?u.containerType!=="normal":!1)||!o&&(u.backdropFilter?u.backdropFilter!=="none":!1)||!o&&(u.filter?u.filter!=="none":!1)||P2.some(c=>(u.willChange||"").includes(c))||F2.some(c=>(u.contain||"").includes(c))}function W2(a){let o=ll(a);for(;rn(o)&&!Aa(o);){if(Vs(o))return o;if(Rr(o))return null;o=ll(o)}return null}function Xs(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const I2=new Set(["html","body","#document"]);function Aa(a){return I2.has(Ma(a))}function Pt(a){return Dt(a).getComputedStyle(a)}function Cr(a){return $t(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function ll(a){if(Ma(a)==="html")return a;const o=a.assignedSlot||a.parentNode||Mv(a)&&a.host||cn(a);return Mv(o)?o.host:o}function cg(a){const o=ll(a);return Aa(o)?a.ownerDocument?a.ownerDocument.body:a.body:rn(o)&&Yi(o)?o:cg(o)}function Li(a,o,u){var c;o===void 0&&(o=[]),u===void 0&&(u=!0);const s=cg(a),d=s===((c=a.ownerDocument)==null?void 0:c.body),h=Dt(s);if(d){const v=ys(h);return o.concat(h,h.visualViewport||[],Yi(s)?s:[],v&&u?Li(v):[])}return o.concat(s,Li(s,[],u))}function ys(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function ug(a){const o=Pt(a);let u=parseFloat(o.width)||0,c=parseFloat(o.height)||0;const s=rn(a),d=s?a.offsetWidth:u,h=s?a.offsetHeight:c,v=yr(u)!==d||yr(c)!==h;return v&&(u=d,c=h),{width:u,height:c,$:v}}function Qs(a){return $t(a)?a:a.contextElement}function Ea(a){const o=Qs(a);if(!rn(o))return on(1);const u=o.getBoundingClientRect(),{width:c,height:s,$:d}=ug(o);let h=(d?yr(u.width):u.width)/c,v=(d?yr(u.height):u.height)/s;return(!h||!Number.isFinite(h))&&(h=1),(!v||!Number.isFinite(v))&&(v=1),{x:h,y:v}}const ew=on(0);function sg(a){const o=Dt(a);return!Xs()||!o.visualViewport?ew:{x:o.visualViewport.offsetLeft,y:o.visualViewport.offsetTop}}function tw(a,o,u){return o===void 0&&(o=!1),!u||o&&u!==Dt(a)?!1:o}function Cl(a,o,u,c){o===void 0&&(o=!1),u===void 0&&(u=!1);const s=a.getBoundingClientRect(),d=Qs(a);let h=on(1);o&&(c?$t(c)&&(h=Ea(c)):h=Ea(a));const v=tw(d,u,c)?sg(d):on(0);let y=(s.left+v.x)/h.x,g=(s.top+v.y)/h.y,x=s.width/h.x,w=s.height/h.y;if(d){const R=Dt(d),N=c&&$t(c)?Dt(c):c;let z=R,T=ys(z);for(;T&&c&&N!==z;){const D=Ea(T),L=T.getBoundingClientRect(),k=Pt(T),_=L.left+(T.clientLeft+parseFloat(k.paddingLeft))*D.x,Z=L.top+(T.clientTop+parseFloat(k.paddingTop))*D.y;y*=D.x,g*=D.y,x*=D.x,w*=D.y,y+=_,g+=Z,z=Dt(T),T=ys(z)}}return xr({width:x,height:w,x:y,y:g})}function Zs(a,o){const u=Cr(a).scrollLeft;return o?o.left+u:Cl(cn(a)).left+u}function fg(a,o,u){u===void 0&&(u=!1);const c=a.getBoundingClientRect(),s=c.left+o.scrollLeft-(u?0:Zs(a,c)),d=c.top+o.scrollTop;return{x:s,y:d}}function nw(a){let{elements:o,rect:u,offsetParent:c,strategy:s}=a;const d=s==="fixed",h=cn(c),v=o?Rr(o.floating):!1;if(c===h||v&&d)return u;let y={scrollLeft:0,scrollTop:0},g=on(1);const x=on(0),w=rn(c);if((w||!w&&!d)&&((Ma(c)!=="body"||Yi(h))&&(y=Cr(c)),rn(c))){const N=Cl(c);g=Ea(c),x.x=N.x+c.clientLeft,x.y=N.y+c.clientTop}const R=h&&!w&&!d?fg(h,y,!0):on(0);return{width:u.width*g.x,height:u.height*g.y,x:u.x*g.x-y.scrollLeft*g.x+x.x+R.x,y:u.y*g.y-y.scrollTop*g.y+x.y+R.y}}function lw(a){return Array.from(a.getClientRects())}function aw(a){const o=cn(a),u=Cr(a),c=a.ownerDocument.body,s=Ct(o.scrollWidth,o.clientWidth,c.scrollWidth,c.clientWidth),d=Ct(o.scrollHeight,o.clientHeight,c.scrollHeight,c.clientHeight);let h=-u.scrollLeft+Zs(a);const v=-u.scrollTop;return Pt(c).direction==="rtl"&&(h+=Ct(o.clientWidth,c.clientWidth)-s),{width:s,height:d,x:h,y:v}}function iw(a,o){const u=Dt(a),c=cn(a),s=u.visualViewport;let d=c.clientWidth,h=c.clientHeight,v=0,y=0;if(s){d=s.width,h=s.height;const g=Xs();(!g||g&&o==="fixed")&&(v=s.offsetLeft,y=s.offsetTop)}return{width:d,height:h,x:v,y}}const ow=new Set(["absolute","fixed"]);function rw(a,o){const u=Cl(a,!0,o==="fixed"),c=u.top+a.clientTop,s=u.left+a.clientLeft,d=rn(a)?Ea(a):on(1),h=a.clientWidth*d.x,v=a.clientHeight*d.y,y=s*d.x,g=c*d.y;return{width:h,height:v,x:y,y:g}}function Ov(a,o,u){let c;if(o==="viewport")c=iw(a,u);else if(o==="document")c=aw(cn(a));else if($t(o))c=rw(o,u);else{const s=sg(a);c={x:o.x-s.x,y:o.y-s.y,width:o.width,height:o.height}}return xr(c)}function dg(a,o){const u=ll(a);return u===o||!$t(u)||Aa(u)?!1:Pt(u).position==="fixed"||dg(u,o)}function cw(a,o){const u=o.get(a);if(u)return u;let c=Li(a,[],!1).filter(v=>$t(v)&&Ma(v)!=="body"),s=null;const d=Pt(a).position==="fixed";let h=d?ll(a):a;for(;$t(h)&&!Aa(h);){const v=Pt(h),y=Vs(h);!y&&v.position==="fixed"&&(s=null),(d?!y&&!s:!y&&v.position==="static"&&!!s&&ow.has(s.position)||Yi(h)&&!y&&dg(a,h))?c=c.filter(x=>x!==h):s=v,h=ll(h)}return o.set(a,c),c}function uw(a){let{element:o,boundary:u,rootBoundary:c,strategy:s}=a;const h=[...u==="clippingAncestors"?Rr(o)?[]:cw(o,this._c):[].concat(u),c],v=h[0],y=h.reduce((g,x)=>{const w=Ov(o,x,s);return g.top=Ct(w.top,g.top),g.right=nl(w.right,g.right),g.bottom=nl(w.bottom,g.bottom),g.left=Ct(w.left,g.left),g},Ov(o,v,s));return{width:y.right-y.left,height:y.bottom-y.top,x:y.left,y:y.top}}function sw(a){const{width:o,height:u}=ug(a);return{width:o,height:u}}function fw(a,o,u){const c=rn(o),s=cn(o),d=u==="fixed",h=Cl(a,!0,d,o);let v={scrollLeft:0,scrollTop:0};const y=on(0);function g(){y.x=Zs(s)}if(c||!c&&!d)if((Ma(o)!=="body"||Yi(s))&&(v=Cr(o)),c){const N=Cl(o,!0,d,o);y.x=N.x+o.clientLeft,y.y=N.y+o.clientTop}else s&&g();d&&!c&&s&&g();const x=s&&!c&&!d?fg(s,v):on(0),w=h.left+v.scrollLeft-y.x-x.x,R=h.top+v.scrollTop-y.y-x.y;return{x:w,y:R,width:h.width,height:h.height}}function os(a){return Pt(a).position==="static"}function _v(a,o){if(!rn(a)||Pt(a).position==="fixed")return null;if(o)return o(a);let u=a.offsetParent;return cn(a)===u&&(u=u.ownerDocument.body),u}function mg(a,o){const u=Dt(a);if(Rr(a))return u;if(!rn(a)){let s=ll(a);for(;s&&!Aa(s);){if($t(s)&&!os(s))return s;s=ll(s)}return u}let c=_v(a,o);for(;c&&K2(c)&&os(c);)c=_v(c,o);return c&&Aa(c)&&os(c)&&!Vs(c)?u:c||W2(a)||u}const dw=async function(a){const o=this.getOffsetParent||mg,u=this.getDimensions,c=await u(a.floating);return{reference:fw(a.reference,await o(a.floating),a.strategy),floating:{x:0,y:0,width:c.width,height:c.height}}};function mw(a){return Pt(a).direction==="rtl"}const hw={convertOffsetParentRelativeRectToViewportRelativeRect:nw,getDocumentElement:cn,getClippingRect:uw,getOffsetParent:mg,getElementRects:dw,getClientRects:lw,getDimensions:sw,getScale:Ea,isElement:$t,isRTL:mw};function hg(a,o){return a.x===o.x&&a.y===o.y&&a.width===o.width&&a.height===o.height}function vw(a,o){let u=null,c;const s=cn(a);function d(){var v;clearTimeout(c),(v=u)==null||v.disconnect(),u=null}function h(v,y){v===void 0&&(v=!1),y===void 0&&(y=1),d();const g=a.getBoundingClientRect(),{left:x,top:w,width:R,height:N}=g;if(v||o(),!R||!N)return;const z=fr(w),T=fr(s.clientWidth-(x+R)),D=fr(s.clientHeight-(w+N)),L=fr(x),_={rootMargin:-z+"px "+-T+"px "+-D+"px "+-L+"px",threshold:Ct(0,nl(1,y))||1};let Z=!0;function G(I){const $=I[0].intersectionRatio;if($!==y){if(!Z)return h();$?h(!1,$):c=setTimeout(()=>{h(!1,1e-7)},1e3)}$===1&&!hg(g,a.getBoundingClientRect())&&h(),Z=!1}try{u=new IntersectionObserver(G,{..._,root:s.ownerDocument})}catch{u=new IntersectionObserver(G,_)}u.observe(a)}return h(!0),d}function pw(a,o,u,c){c===void 0&&(c={});const{ancestorScroll:s=!0,ancestorResize:d=!0,elementResize:h=typeof ResizeObserver=="function",layoutShift:v=typeof IntersectionObserver=="function",animationFrame:y=!1}=c,g=Qs(a),x=s||d?[...g?Li(g):[],...Li(o)]:[];x.forEach(L=>{s&&L.addEventListener("scroll",u,{passive:!0}),d&&L.addEventListener("resize",u)});const w=g&&v?vw(g,u):null;let R=-1,N=null;h&&(N=new ResizeObserver(L=>{let[k]=L;k&&k.target===g&&N&&(N.unobserve(o),cancelAnimationFrame(R),R=requestAnimationFrame(()=>{var _;(_=N)==null||_.observe(o)})),u()}),g&&!y&&N.observe(g),N.observe(o));let z,T=y?Cl(a):null;y&&D();function D(){const L=Cl(a);T&&!hg(T,L)&&u(),T=L,z=requestAnimationFrame(D)}return u(),()=>{var L;x.forEach(k=>{s&&k.removeEventListener("scroll",u),d&&k.removeEventListener("resize",u)}),w?.(),(L=N)==null||L.disconnect(),N=null,y&&cancelAnimationFrame(z)}}const gw=G2,yw=Y2,bw=L2,xw=X2,Sw=k2,jv=B2,ww=V2,Ew=(a,o,u)=>{const c=new Map,s={platform:hw,...u},d={...s.platform,_c:c};return H2(a,o,{...s,platform:d})};var Tw=typeof document<"u",Aw=function(){},pr=Tw?b.useLayoutEffect:Aw;function Sr(a,o){if(a===o)return!0;if(typeof a!=typeof o)return!1;if(typeof a=="function"&&a.toString()===o.toString())return!0;let u,c,s;if(a&&o&&typeof a=="object"){if(Array.isArray(a)){if(u=a.length,u!==o.length)return!1;for(c=u;c--!==0;)if(!Sr(a[c],o[c]))return!1;return!0}if(s=Object.keys(a),u=s.length,u!==Object.keys(o).length)return!1;for(c=u;c--!==0;)if(!{}.hasOwnProperty.call(o,s[c]))return!1;for(c=u;c--!==0;){const d=s[c];if(!(d==="_owner"&&a.$$typeof)&&!Sr(a[d],o[d]))return!1}return!0}return a!==a&&o!==o}function vg(a){return typeof window>"u"?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function zv(a,o){const u=vg(a);return Math.round(o*u)/u}function rs(a){const o=b.useRef(a);return pr(()=>{o.current=a}),o}function Nw(a){a===void 0&&(a={});const{placement:o="bottom",strategy:u="absolute",middleware:c=[],platform:s,elements:{reference:d,floating:h}={},transform:v=!0,whileElementsMounted:y,open:g}=a,[x,w]=b.useState({x:0,y:0,strategy:u,placement:o,middlewareData:{},isPositioned:!1}),[R,N]=b.useState(c);Sr(R,c)||N(c);const[z,T]=b.useState(null),[D,L]=b.useState(null),k=b.useCallback(B=>{B!==I.current&&(I.current=B,T(B))},[]),_=b.useCallback(B=>{B!==$.current&&($.current=B,L(B))},[]),Z=d||z,G=h||D,I=b.useRef(null),$=b.useRef(null),K=b.useRef(x),oe=y!=null,ve=rs(y),pe=rs(s),se=rs(g),ge=b.useCallback(()=>{if(!I.current||!$.current)return;const B={placement:o,strategy:u,middleware:R};pe.current&&(B.platform=pe.current),Ew(I.current,$.current,B).then(ae=>{const E={...ae,isPositioned:se.current!==!1};me.current&&!Sr(K.current,E)&&(K.current=E,ki.flushSync(()=>{w(E)}))})},[R,o,u,pe,se]);pr(()=>{g===!1&&K.current.isPositioned&&(K.current.isPositioned=!1,w(B=>({...B,isPositioned:!1})))},[g]);const me=b.useRef(!1);pr(()=>(me.current=!0,()=>{me.current=!1}),[]),pr(()=>{if(Z&&(I.current=Z),G&&($.current=G),Z&&G){if(ve.current)return ve.current(Z,G,ge);ge()}},[Z,G,ge,ve,oe]);const fe=b.useMemo(()=>({reference:I,floating:$,setReference:k,setFloating:_}),[k,_]),M=b.useMemo(()=>({reference:Z,floating:G}),[Z,G]),Q=b.useMemo(()=>{const B={position:u,left:0,top:0};if(!M.floating)return B;const ae=zv(M.floating,x.x),E=zv(M.floating,x.y);return v?{...B,transform:"translate("+ae+"px, "+E+"px)",...vg(M.floating)>=1.5&&{willChange:"transform"}}:{position:u,left:ae,top:E}},[u,v,M.floating,x.x,x.y]);return b.useMemo(()=>({...x,update:ge,refs:fe,elements:M,floatingStyles:Q}),[x,ge,fe,M,Q])}const Rw=a=>{function o(u){return{}.hasOwnProperty.call(u,"current")}return{name:"arrow",options:a,fn(u){const{element:c,padding:s}=typeof a=="function"?a(u):a;return c&&o(c)?c.current!=null?jv({element:c.current,padding:s}).fn(u):{}:c?jv({element:c,padding:s}).fn(u):{}}}},Cw=(a,o)=>({...gw(a),options:[a,o]}),Dw=(a,o)=>({...yw(a),options:[a,o]}),Mw=(a,o)=>({...ww(a),options:[a,o]}),Ow=(a,o)=>({...bw(a),options:[a,o]}),_w=(a,o)=>({...xw(a),options:[a,o]}),jw=(a,o)=>({...Sw(a),options:[a,o]}),zw=(a,o)=>({...Rw(a),options:[a,o]});var Uw="Arrow",pg=b.forwardRef((a,o)=>{const{children:u,width:c=10,height:s=5,...d}=a;return m.jsx(De.svg,{...d,ref:o,width:c,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?u:m.jsx("polygon",{points:"0,0 30,0 15,10"})})});pg.displayName=Uw;var Hw=pg;function Bw(a){const[o,u]=b.useState(void 0);return ft(()=>{if(a){u({width:a.offsetWidth,height:a.offsetHeight});const c=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const d=s[0];let h,v;if("borderBoxSize"in d){const y=d.borderBoxSize,g=Array.isArray(y)?y[0]:y;h=g.inlineSize,v=g.blockSize}else h=a.offsetWidth,v=a.offsetHeight;u({width:h,height:v})});return c.observe(a,{box:"border-box"}),()=>c.unobserve(a)}else u(void 0)},[a]),o}var Ks="Popper",[gg,yg]=Ca(Ks),[Lw,bg]=gg(Ks),xg=a=>{const{__scopePopper:o,children:u}=a,[c,s]=b.useState(null);return m.jsx(Lw,{scope:o,anchor:c,onAnchorChange:s,children:u})};xg.displayName=Ks;var Sg="PopperAnchor",wg=b.forwardRef((a,o)=>{const{__scopePopper:u,virtualRef:c,...s}=a,d=bg(Sg,u),h=b.useRef(null),v=Ze(o,h);return b.useEffect(()=>{d.onAnchorChange(c?.current||h.current)}),c?null:m.jsx(De.div,{...s,ref:v})});wg.displayName=Sg;var Js="PopperContent",[kw,qw]=gg(Js),Eg=b.forwardRef((a,o)=>{const{__scopePopper:u,side:c="bottom",sideOffset:s=0,align:d="center",alignOffset:h=0,arrowPadding:v=0,avoidCollisions:y=!0,collisionBoundary:g=[],collisionPadding:x=0,sticky:w="partial",hideWhenDetached:R=!1,updatePositionStrategy:N="optimized",onPlaced:z,...T}=a,D=bg(Js,u),[L,k]=b.useState(null),_=Ze(o,P=>k(P)),[Z,G]=b.useState(null),I=Bw(Z),$=I?.width??0,K=I?.height??0,oe=c+(d!=="center"?"-"+d:""),ve=typeof x=="number"?x:{top:0,right:0,bottom:0,left:0,...x},pe=Array.isArray(g)?g:[g],se=pe.length>0,ge={padding:ve,boundary:pe.filter(Yw),altBoundary:se},{refs:me,floatingStyles:fe,placement:M,isPositioned:Q,middlewareData:B}=Nw({strategy:"fixed",placement:oe,whileElementsMounted:(...P)=>pw(...P,{animationFrame:N==="always"}),elements:{reference:D.anchor},middleware:[Cw({mainAxis:s+K,alignmentAxis:h}),y&&Dw({mainAxis:!0,crossAxis:!1,limiter:w==="partial"?Mw():void 0,...ge}),y&&Ow({...ge}),_w({...ge,apply:({elements:P,rects:ce,availableWidth:_e,availableHeight:Ae})=>{const{width:Ce,height:je}=ce.reference,rt=P.floating.style;rt.setProperty("--radix-popper-available-width",`${_e}px`),rt.setProperty("--radix-popper-available-height",`${Ae}px`),rt.setProperty("--radix-popper-anchor-width",`${Ce}px`),rt.setProperty("--radix-popper-anchor-height",`${je}px`)}}),Z&&zw({element:Z,padding:v}),Vw({arrowWidth:$,arrowHeight:K}),R&&jw({strategy:"referenceHidden",...ge})]}),[ae,E]=Ng(M),V=tl(z);ft(()=>{Q&&V?.()},[Q,V]);const F=B.arrow?.x,J=B.arrow?.y,W=B.arrow?.centerOffset!==0,[he,ie]=b.useState();return ft(()=>{L&&ie(window.getComputedStyle(L).zIndex)},[L]),m.jsx("div",{ref:me.setFloating,"data-radix-popper-content-wrapper":"",style:{...fe,transform:Q?fe.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:he,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:m.jsx(kw,{scope:u,placedSide:ae,onArrowChange:G,arrowX:F,arrowY:J,shouldHideArrow:W,children:m.jsx(De.div,{"data-side":ae,"data-align":E,...T,ref:_,style:{...T.style,animation:Q?void 0:"none"}})})})});Eg.displayName=Js;var Tg="PopperArrow",Gw={top:"bottom",right:"left",bottom:"top",left:"right"},Ag=b.forwardRef(function(o,u){const{__scopePopper:c,...s}=o,d=qw(Tg,c),h=Gw[d.placedSide];return m.jsx("span",{ref:d.onArrowChange,style:{position:"absolute",left:d.arrowX,top:d.arrowY,[h]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[d.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[d.placedSide],visibility:d.shouldHideArrow?"hidden":void 0},children:m.jsx(Hw,{...s,ref:u,style:{...s.style,display:"block"}})})});Ag.displayName=Tg;function Yw(a){return a!==null}var Vw=a=>({name:"transformOrigin",options:a,fn(o){const{placement:u,rects:c,middlewareData:s}=o,h=s.arrow?.centerOffset!==0,v=h?0:a.arrowWidth,y=h?0:a.arrowHeight,[g,x]=Ng(u),w={start:"0%",center:"50%",end:"100%"}[x],R=(s.arrow?.x??0)+v/2,N=(s.arrow?.y??0)+y/2;let z="",T="";return g==="bottom"?(z=h?w:`${R}px`,T=`${-y}px`):g==="top"?(z=h?w:`${R}px`,T=`${c.floating.height+y}px`):g==="right"?(z=`${-y}px`,T=h?w:`${N}px`):g==="left"&&(z=`${c.floating.width+y}px`,T=h?w:`${N}px`),{data:{x:z,y:T}}}});function Ng(a){const[o,u="center"]=a.split("-");return[o,u]}var Xw=xg,Qw=wg,Zw=Eg,Kw=Ag;function Jw(a){const o=b.useRef({value:a,previous:a});return b.useMemo(()=>(o.current.value!==a&&(o.current.previous=o.current.value,o.current.value=a),o.current.previous),[a])}var Rg=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),$w="VisuallyHidden",Pw=b.forwardRef((a,o)=>m.jsx(De.span,{...a,ref:o,style:{...Rg,...a.style}}));Pw.displayName=$w;var Fw=[" ","Enter","ArrowUp","ArrowDown"],Ww=[" ","Enter"],Dl="Select",[Dr,Mr,Iw]=Iv(Dl),[Oa,HE]=Ca(Dl,[Iw,yg]),Or=yg(),[eE,al]=Oa(Dl),[tE,nE]=Oa(Dl),Cg=a=>{const{__scopeSelect:o,children:u,open:c,defaultOpen:s,onOpenChange:d,value:h,defaultValue:v,onValueChange:y,dir:g,name:x,autoComplete:w,disabled:R,required:N,form:z}=a,T=Or(o),[D,L]=b.useState(null),[k,_]=b.useState(null),[Z,G]=b.useState(!1),I=Rs(g),[$,K]=Hi({prop:c,defaultProp:s??!1,onChange:d,caller:Dl}),[oe,ve]=Hi({prop:h,defaultProp:v,onChange:y,caller:Dl}),pe=b.useRef(null),se=D?z||!!D.closest("form"):!0,[ge,me]=b.useState(new Set),fe=Array.from(ge).map(M=>M.props.value).join(";");return m.jsx(Xw,{...T,children:m.jsxs(eE,{required:N,scope:o,trigger:D,onTriggerChange:L,valueNode:k,onValueNodeChange:_,valueNodeHasChildren:Z,onValueNodeHasChildrenChange:G,contentId:el(),value:oe,onValueChange:ve,open:$,onOpenChange:K,dir:I,triggerPointerDownPosRef:pe,disabled:R,children:[m.jsx(Dr.Provider,{scope:o,children:m.jsx(tE,{scope:a.__scopeSelect,onNativeOptionAdd:b.useCallback(M=>{me(Q=>new Set(Q).add(M))},[]),onNativeOptionRemove:b.useCallback(M=>{me(Q=>{const B=new Set(Q);return B.delete(M),B})},[]),children:u})}),se?m.jsxs(Wg,{"aria-hidden":!0,required:N,tabIndex:-1,name:x,autoComplete:w,value:oe,onChange:M=>ve(M.target.value),disabled:R,form:z,children:[oe===void 0?m.jsx("option",{value:""}):null,Array.from(ge)]},fe):null]})})};Cg.displayName=Dl;var Dg="SelectTrigger",Mg=b.forwardRef((a,o)=>{const{__scopeSelect:u,disabled:c=!1,...s}=a,d=Or(u),h=al(Dg,u),v=h.disabled||c,y=Ze(o,h.onTriggerChange),g=Mr(u),x=b.useRef("touch"),[w,R,N]=ey(T=>{const D=g().filter(_=>!_.disabled),L=D.find(_=>_.value===h.value),k=ty(D,T,L);k!==void 0&&h.onValueChange(k.value)}),z=T=>{v||(h.onOpenChange(!0),N()),T&&(h.triggerPointerDownPosRef.current={x:Math.round(T.pageX),y:Math.round(T.pageY)})};return m.jsx(Qw,{asChild:!0,...d,children:m.jsx(De.button,{type:"button",role:"combobox","aria-controls":h.contentId,"aria-expanded":h.open,"aria-required":h.required,"aria-autocomplete":"none",dir:h.dir,"data-state":h.open?"open":"closed",disabled:v,"data-disabled":v?"":void 0,"data-placeholder":Ig(h.value)?"":void 0,...s,ref:y,onClick:Re(s.onClick,T=>{T.currentTarget.focus(),x.current!=="mouse"&&z(T)}),onPointerDown:Re(s.onPointerDown,T=>{x.current=T.pointerType;const D=T.target;D.hasPointerCapture(T.pointerId)&&D.releasePointerCapture(T.pointerId),T.button===0&&T.ctrlKey===!1&&T.pointerType==="mouse"&&(z(T),T.preventDefault())}),onKeyDown:Re(s.onKeyDown,T=>{const D=w.current!=="";!(T.ctrlKey||T.altKey||T.metaKey)&&T.key.length===1&&R(T.key),!(D&&T.key===" ")&&Fw.includes(T.key)&&(z(),T.preventDefault())})})})});Mg.displayName=Dg;var Og="SelectValue",_g=b.forwardRef((a,o)=>{const{__scopeSelect:u,className:c,style:s,children:d,placeholder:h="",...v}=a,y=al(Og,u),{onValueNodeHasChildrenChange:g}=y,x=d!==void 0,w=Ze(o,y.onValueNodeChange);return ft(()=>{g(x)},[g,x]),m.jsx(De.span,{...v,ref:w,style:{pointerEvents:"none"},children:Ig(y.value)?m.jsx(m.Fragment,{children:h}):d})});_g.displayName=Og;var lE="SelectIcon",jg=b.forwardRef((a,o)=>{const{__scopeSelect:u,children:c,...s}=a;return m.jsx(De.span,{"aria-hidden":!0,...s,ref:o,children:c||"▼"})});jg.displayName=lE;var aE="SelectPortal",zg=a=>m.jsx(Os,{asChild:!0,...a});zg.displayName=aE;var Ml="SelectContent",Ug=b.forwardRef((a,o)=>{const u=al(Ml,a.__scopeSelect),[c,s]=b.useState();if(ft(()=>{s(new DocumentFragment)},[]),!u.open){const d=c;return d?ki.createPortal(m.jsx(Hg,{scope:a.__scopeSelect,children:m.jsx(Dr.Slot,{scope:a.__scopeSelect,children:m.jsx("div",{children:a.children})})}),d):null}return m.jsx(Bg,{...a,ref:o})});Ug.displayName=Ml;var Xt=10,[Hg,il]=Oa(Ml),iE="SelectContentImpl",oE=Ta("SelectContent.RemoveScroll"),Bg=b.forwardRef((a,o)=>{const{__scopeSelect:u,position:c="item-aligned",onCloseAutoFocus:s,onEscapeKeyDown:d,onPointerDownOutside:h,side:v,sideOffset:y,align:g,alignOffset:x,arrowPadding:w,collisionBoundary:R,collisionPadding:N,sticky:z,hideWhenDetached:T,avoidCollisions:D,...L}=a,k=al(Ml,u),[_,Z]=b.useState(null),[G,I]=b.useState(null),$=Ze(o,P=>Z(P)),[K,oe]=b.useState(null),[ve,pe]=b.useState(null),se=Mr(u),[ge,me]=b.useState(!1),fe=b.useRef(!1);b.useEffect(()=>{if(_)return Up(_)},[_]),Np();const M=b.useCallback(P=>{const[ce,..._e]=se().map(je=>je.ref.current),[Ae]=_e.slice(-1),Ce=document.activeElement;for(const je of P)if(je===Ce||(je?.scrollIntoView({block:"nearest"}),je===ce&&G&&(G.scrollTop=0),je===Ae&&G&&(G.scrollTop=G.scrollHeight),je?.focus(),document.activeElement!==Ce))return},[se,G]),Q=b.useCallback(()=>M([K,_]),[M,K,_]);b.useEffect(()=>{ge&&Q()},[ge,Q]);const{onOpenChange:B,triggerPointerDownPosRef:ae}=k;b.useEffect(()=>{if(_){let P={x:0,y:0};const ce=Ae=>{P={x:Math.abs(Math.round(Ae.pageX)-(ae.current?.x??0)),y:Math.abs(Math.round(Ae.pageY)-(ae.current?.y??0))}},_e=Ae=>{P.x<=10&&P.y<=10?Ae.preventDefault():_.contains(Ae.target)||B(!1),document.removeEventListener("pointermove",ce),ae.current=null};return ae.current!==null&&(document.addEventListener("pointermove",ce),document.addEventListener("pointerup",_e,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",ce),document.removeEventListener("pointerup",_e,{capture:!0})}}},[_,B,ae]),b.useEffect(()=>{const P=()=>B(!1);return window.addEventListener("blur",P),window.addEventListener("resize",P),()=>{window.removeEventListener("blur",P),window.removeEventListener("resize",P)}},[B]);const[E,V]=ey(P=>{const ce=se().filter(Ce=>!Ce.disabled),_e=ce.find(Ce=>Ce.ref.current===document.activeElement),Ae=ty(ce,P,_e);Ae&&setTimeout(()=>Ae.ref.current.focus())}),F=b.useCallback((P,ce,_e)=>{const Ae=!fe.current&&!_e;(k.value!==void 0&&k.value===ce||Ae)&&(oe(P),Ae&&(fe.current=!0))},[k.value]),J=b.useCallback(()=>_?.focus(),[_]),W=b.useCallback((P,ce,_e)=>{const Ae=!fe.current&&!_e;(k.value!==void 0&&k.value===ce||Ae)&&pe(P)},[k.value]),he=c==="popper"?bs:Lg,ie=he===bs?{side:v,sideOffset:y,align:g,alignOffset:x,arrowPadding:w,collisionBoundary:R,collisionPadding:N,sticky:z,hideWhenDetached:T,avoidCollisions:D}:{};return m.jsx(Hg,{scope:u,content:_,viewport:G,onViewportChange:I,itemRefCallback:F,selectedItem:K,onItemLeave:J,itemTextRefCallback:W,focusSelectedItem:Q,selectedItemText:ve,position:c,isPositioned:ge,searchRef:E,children:m.jsx(_s,{as:oE,allowPinchZoom:!0,children:m.jsx(Ms,{asChild:!0,trapped:k.open,onMountAutoFocus:P=>{P.preventDefault()},onUnmountAutoFocus:Re(s,P=>{k.trigger?.focus({preventScroll:!0}),P.preventDefault()}),children:m.jsx(Ds,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:d,onPointerDownOutside:h,onFocusOutside:P=>P.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:m.jsx(he,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:P=>P.preventDefault(),...L,...ie,onPlaced:()=>me(!0),ref:$,style:{display:"flex",flexDirection:"column",outline:"none",...L.style},onKeyDown:Re(L.onKeyDown,P=>{const ce=P.ctrlKey||P.altKey||P.metaKey;if(P.key==="Tab"&&P.preventDefault(),!ce&&P.key.length===1&&V(P.key),["ArrowUp","ArrowDown","Home","End"].includes(P.key)){let Ae=se().filter(Ce=>!Ce.disabled).map(Ce=>Ce.ref.current);if(["ArrowUp","End"].includes(P.key)&&(Ae=Ae.slice().reverse()),["ArrowUp","ArrowDown"].includes(P.key)){const Ce=P.target,je=Ae.indexOf(Ce);Ae=Ae.slice(je+1)}setTimeout(()=>M(Ae)),P.preventDefault()}})})})})})})});Bg.displayName=iE;var rE="SelectItemAlignedPosition",Lg=b.forwardRef((a,o)=>{const{__scopeSelect:u,onPlaced:c,...s}=a,d=al(Ml,u),h=il(Ml,u),[v,y]=b.useState(null),[g,x]=b.useState(null),w=Ze(o,$=>x($)),R=Mr(u),N=b.useRef(!1),z=b.useRef(!0),{viewport:T,selectedItem:D,selectedItemText:L,focusSelectedItem:k}=h,_=b.useCallback(()=>{if(d.trigger&&d.valueNode&&v&&g&&T&&D&&L){const $=d.trigger.getBoundingClientRect(),K=g.getBoundingClientRect(),oe=d.valueNode.getBoundingClientRect(),ve=L.getBoundingClientRect();if(d.dir!=="rtl"){const Ce=ve.left-K.left,je=oe.left-Ce,rt=$.left-je,qt=$.width+rt,_a=Math.max(qt,K.width),ja=window.innerWidth-Xt,mt=Tv(je,[Xt,Math.max(Xt,ja-_a)]);v.style.minWidth=qt+"px",v.style.left=mt+"px"}else{const Ce=K.right-ve.right,je=window.innerWidth-oe.right-Ce,rt=window.innerWidth-$.right-je,qt=$.width+rt,_a=Math.max(qt,K.width),ja=window.innerWidth-Xt,mt=Tv(je,[Xt,Math.max(Xt,ja-_a)]);v.style.minWidth=qt+"px",v.style.right=mt+"px"}const pe=R(),se=window.innerHeight-Xt*2,ge=T.scrollHeight,me=window.getComputedStyle(g),fe=parseInt(me.borderTopWidth,10),M=parseInt(me.paddingTop,10),Q=parseInt(me.borderBottomWidth,10),B=parseInt(me.paddingBottom,10),ae=fe+M+ge+B+Q,E=Math.min(D.offsetHeight*5,ae),V=window.getComputedStyle(T),F=parseInt(V.paddingTop,10),J=parseInt(V.paddingBottom,10),W=$.top+$.height/2-Xt,he=se-W,ie=D.offsetHeight/2,P=D.offsetTop+ie,ce=fe+M+P,_e=ae-ce;if(ce<=W){const Ce=pe.length>0&&D===pe[pe.length-1].ref.current;v.style.bottom="0px";const je=g.clientHeight-T.offsetTop-T.offsetHeight,rt=Math.max(he,ie+(Ce?J:0)+je+Q),qt=ce+rt;v.style.height=qt+"px"}else{const Ce=pe.length>0&&D===pe[0].ref.current;v.style.top="0px";const rt=Math.max(W,fe+T.offsetTop+(Ce?F:0)+ie)+_e;v.style.height=rt+"px",T.scrollTop=ce-W+T.offsetTop}v.style.margin=`${Xt}px 0`,v.style.minHeight=E+"px",v.style.maxHeight=se+"px",c?.(),requestAnimationFrame(()=>N.current=!0)}},[R,d.trigger,d.valueNode,v,g,T,D,L,d.dir,c]);ft(()=>_(),[_]);const[Z,G]=b.useState();ft(()=>{g&&G(window.getComputedStyle(g).zIndex)},[g]);const I=b.useCallback($=>{$&&z.current===!0&&(_(),k?.(),z.current=!1)},[_,k]);return m.jsx(uE,{scope:u,contentWrapper:v,shouldExpandOnScrollRef:N,onScrollButtonChange:I,children:m.jsx("div",{ref:y,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:Z},children:m.jsx(De.div,{...s,ref:w,style:{boxSizing:"border-box",maxHeight:"100%",...s.style}})})})});Lg.displayName=rE;var cE="SelectPopperPosition",bs=b.forwardRef((a,o)=>{const{__scopeSelect:u,align:c="start",collisionPadding:s=Xt,...d}=a,h=Or(u);return m.jsx(Zw,{...h,...d,ref:o,align:c,collisionPadding:s,style:{boxSizing:"border-box",...d.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});bs.displayName=cE;var[uE,$s]=Oa(Ml,{}),xs="SelectViewport",kg=b.forwardRef((a,o)=>{const{__scopeSelect:u,nonce:c,...s}=a,d=il(xs,u),h=$s(xs,u),v=Ze(o,d.onViewportChange),y=b.useRef(0);return m.jsxs(m.Fragment,{children:[m.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:c}),m.jsx(Dr.Slot,{scope:u,children:m.jsx(De.div,{"data-radix-select-viewport":"",role:"presentation",...s,ref:v,style:{position:"relative",flex:1,overflow:"hidden auto",...s.style},onScroll:Re(s.onScroll,g=>{const x=g.currentTarget,{contentWrapper:w,shouldExpandOnScrollRef:R}=h;if(R?.current&&w){const N=Math.abs(y.current-x.scrollTop);if(N>0){const z=window.innerHeight-Xt*2,T=parseFloat(w.style.minHeight),D=parseFloat(w.style.height),L=Math.max(T,D);if(L<z){const k=L+N,_=Math.min(z,k),Z=k-_;w.style.height=_+"px",w.style.bottom==="0px"&&(x.scrollTop=Z>0?Z:0,w.style.justifyContent="flex-end")}}}y.current=x.scrollTop})})})]})});kg.displayName=xs;var qg="SelectGroup",[sE,fE]=Oa(qg),dE=b.forwardRef((a,o)=>{const{__scopeSelect:u,...c}=a,s=el();return m.jsx(sE,{scope:u,id:s,children:m.jsx(De.div,{role:"group","aria-labelledby":s,...c,ref:o})})});dE.displayName=qg;var Gg="SelectLabel",Yg=b.forwardRef((a,o)=>{const{__scopeSelect:u,...c}=a,s=fE(Gg,u);return m.jsx(De.div,{id:s.id,...c,ref:o})});Yg.displayName=Gg;var wr="SelectItem",[mE,Vg]=Oa(wr),Xg=b.forwardRef((a,o)=>{const{__scopeSelect:u,value:c,disabled:s=!1,textValue:d,...h}=a,v=al(wr,u),y=il(wr,u),g=v.value===c,[x,w]=b.useState(d??""),[R,N]=b.useState(!1),z=Ze(o,k=>y.itemRefCallback?.(k,c,s)),T=el(),D=b.useRef("touch"),L=()=>{s||(v.onValueChange(c),v.onOpenChange(!1))};if(c==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return m.jsx(mE,{scope:u,value:c,disabled:s,textId:T,isSelected:g,onItemTextChange:b.useCallback(k=>{w(_=>_||(k?.textContent??"").trim())},[]),children:m.jsx(Dr.ItemSlot,{scope:u,value:c,disabled:s,textValue:x,children:m.jsx(De.div,{role:"option","aria-labelledby":T,"data-highlighted":R?"":void 0,"aria-selected":g&&R,"data-state":g?"checked":"unchecked","aria-disabled":s||void 0,"data-disabled":s?"":void 0,tabIndex:s?void 0:-1,...h,ref:z,onFocus:Re(h.onFocus,()=>N(!0)),onBlur:Re(h.onBlur,()=>N(!1)),onClick:Re(h.onClick,()=>{D.current!=="mouse"&&L()}),onPointerUp:Re(h.onPointerUp,()=>{D.current==="mouse"&&L()}),onPointerDown:Re(h.onPointerDown,k=>{D.current=k.pointerType}),onPointerMove:Re(h.onPointerMove,k=>{D.current=k.pointerType,s?y.onItemLeave?.():D.current==="mouse"&&k.currentTarget.focus({preventScroll:!0})}),onPointerLeave:Re(h.onPointerLeave,k=>{k.currentTarget===document.activeElement&&y.onItemLeave?.()}),onKeyDown:Re(h.onKeyDown,k=>{y.searchRef?.current!==""&&k.key===" "||(Ww.includes(k.key)&&L(),k.key===" "&&k.preventDefault())})})})})});Xg.displayName=wr;var Ui="SelectItemText",Qg=b.forwardRef((a,o)=>{const{__scopeSelect:u,className:c,style:s,...d}=a,h=al(Ui,u),v=il(Ui,u),y=Vg(Ui,u),g=nE(Ui,u),[x,w]=b.useState(null),R=Ze(o,L=>w(L),y.onItemTextChange,L=>v.itemTextRefCallback?.(L,y.value,y.disabled)),N=x?.textContent,z=b.useMemo(()=>m.jsx("option",{value:y.value,disabled:y.disabled,children:N},y.value),[y.disabled,y.value,N]),{onNativeOptionAdd:T,onNativeOptionRemove:D}=g;return ft(()=>(T(z),()=>D(z)),[T,D,z]),m.jsxs(m.Fragment,{children:[m.jsx(De.span,{id:y.textId,...d,ref:R}),y.isSelected&&h.valueNode&&!h.valueNodeHasChildren?ki.createPortal(d.children,h.valueNode):null]})});Qg.displayName=Ui;var Zg="SelectItemIndicator",Kg=b.forwardRef((a,o)=>{const{__scopeSelect:u,...c}=a;return Vg(Zg,u).isSelected?m.jsx(De.span,{"aria-hidden":!0,...c,ref:o}):null});Kg.displayName=Zg;var Ss="SelectScrollUpButton",Jg=b.forwardRef((a,o)=>{const u=il(Ss,a.__scopeSelect),c=$s(Ss,a.__scopeSelect),[s,d]=b.useState(!1),h=Ze(o,c.onScrollButtonChange);return ft(()=>{if(u.viewport&&u.isPositioned){let v=function(){const g=y.scrollTop>0;d(g)};const y=u.viewport;return v(),y.addEventListener("scroll",v),()=>y.removeEventListener("scroll",v)}},[u.viewport,u.isPositioned]),s?m.jsx(Pg,{...a,ref:h,onAutoScroll:()=>{const{viewport:v,selectedItem:y}=u;v&&y&&(v.scrollTop=v.scrollTop-y.offsetHeight)}}):null});Jg.displayName=Ss;var ws="SelectScrollDownButton",$g=b.forwardRef((a,o)=>{const u=il(ws,a.__scopeSelect),c=$s(ws,a.__scopeSelect),[s,d]=b.useState(!1),h=Ze(o,c.onScrollButtonChange);return ft(()=>{if(u.viewport&&u.isPositioned){let v=function(){const g=y.scrollHeight-y.clientHeight,x=Math.ceil(y.scrollTop)<g;d(x)};const y=u.viewport;return v(),y.addEventListener("scroll",v),()=>y.removeEventListener("scroll",v)}},[u.viewport,u.isPositioned]),s?m.jsx(Pg,{...a,ref:h,onAutoScroll:()=>{const{viewport:v,selectedItem:y}=u;v&&y&&(v.scrollTop=v.scrollTop+y.offsetHeight)}}):null});$g.displayName=ws;var Pg=b.forwardRef((a,o)=>{const{__scopeSelect:u,onAutoScroll:c,...s}=a,d=il("SelectScrollButton",u),h=b.useRef(null),v=Mr(u),y=b.useCallback(()=>{h.current!==null&&(window.clearInterval(h.current),h.current=null)},[]);return b.useEffect(()=>()=>y(),[y]),ft(()=>{v().find(x=>x.ref.current===document.activeElement)?.ref.current?.scrollIntoView({block:"nearest"})},[v]),m.jsx(De.div,{"aria-hidden":!0,...s,ref:o,style:{flexShrink:0,...s.style},onPointerDown:Re(s.onPointerDown,()=>{h.current===null&&(h.current=window.setInterval(c,50))}),onPointerMove:Re(s.onPointerMove,()=>{d.onItemLeave?.(),h.current===null&&(h.current=window.setInterval(c,50))}),onPointerLeave:Re(s.onPointerLeave,()=>{y()})})}),hE="SelectSeparator",Fg=b.forwardRef((a,o)=>{const{__scopeSelect:u,...c}=a;return m.jsx(De.div,{"aria-hidden":!0,...c,ref:o})});Fg.displayName=hE;var Es="SelectArrow",vE=b.forwardRef((a,o)=>{const{__scopeSelect:u,...c}=a,s=Or(u),d=al(Es,u),h=il(Es,u);return d.open&&h.position==="popper"?m.jsx(Kw,{...s,...c,ref:o}):null});vE.displayName=Es;var pE="SelectBubbleInput",Wg=b.forwardRef(({__scopeSelect:a,value:o,...u},c)=>{const s=b.useRef(null),d=Ze(c,s),h=Jw(o);return b.useEffect(()=>{const v=s.current;if(!v)return;const y=window.HTMLSelectElement.prototype,x=Object.getOwnPropertyDescriptor(y,"value").set;if(h!==o&&x){const w=new Event("change",{bubbles:!0});x.call(v,o),v.dispatchEvent(w)}},[h,o]),m.jsx(De.select,{...u,style:{...Rg,...u.style},ref:d,defaultValue:o})});Wg.displayName=pE;function Ig(a){return a===""||a===void 0}function ey(a){const o=tl(a),u=b.useRef(""),c=b.useRef(0),s=b.useCallback(h=>{const v=u.current+h;o(v),function y(g){u.current=g,window.clearTimeout(c.current),g!==""&&(c.current=window.setTimeout(()=>y(""),1e3))}(v)},[o]),d=b.useCallback(()=>{u.current="",window.clearTimeout(c.current)},[]);return b.useEffect(()=>()=>window.clearTimeout(c.current),[]),[u,s,d]}function ty(a,o,u){const s=o.length>1&&Array.from(o).every(g=>g===o[0])?o[0]:o,d=u?a.indexOf(u):-1;let h=gE(a,Math.max(d,0));s.length===1&&(h=h.filter(g=>g!==u));const y=h.find(g=>g.textValue.toLowerCase().startsWith(s.toLowerCase()));return y!==u?y:void 0}function gE(a,o){return a.map((u,c)=>a[(o+c)%a.length])}var yE=Cg,ny=Mg,bE=_g,xE=jg,SE=zg,ly=Ug,wE=kg,ay=Yg,iy=Xg,EE=Qg,TE=Kg,oy=Jg,ry=$g,cy=Fg;const AE=yE,NE=bE,uy=b.forwardRef(({className:a,children:o,...u},c)=>m.jsxs(ny,{ref:c,className:Te("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...u,children:[o,m.jsx(xE,{asChild:!0,children:m.jsx(kv,{className:"h-4 w-4 opacity-50"})})]}));uy.displayName=ny.displayName;const sy=b.forwardRef(({className:a,...o},u)=>m.jsx(oy,{ref:u,className:Te("flex cursor-default items-center justify-center py-1",a),...o,children:m.jsx(nx,{className:"h-4 w-4"})}));sy.displayName=oy.displayName;const fy=b.forwardRef(({className:a,...o},u)=>m.jsx(ry,{ref:u,className:Te("flex cursor-default items-center justify-center py-1",a),...o,children:m.jsx(kv,{className:"h-4 w-4"})}));fy.displayName=ry.displayName;const dy=b.forwardRef(({className:a,children:o,position:u="popper",...c},s)=>m.jsx(SE,{children:m.jsxs(ly,{ref:s,className:Te("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",u==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:u,...c,children:[m.jsx(sy,{}),m.jsx(wE,{className:Te("p-1",u==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:o}),m.jsx(fy,{})]})}));dy.displayName=ly.displayName;const RE=b.forwardRef(({className:a,...o},u)=>m.jsx(ay,{ref:u,className:Te("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...o}));RE.displayName=ay.displayName;const Sa=b.forwardRef(({className:a,children:o,...u},c)=>m.jsxs(iy,{ref:c,className:Te("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...u,children:[m.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:m.jsx(TE,{children:m.jsx(Ib,{className:"h-4 w-4"})})}),m.jsx(EE,{children:o})]}));Sa.displayName=iy.displayName;const CE=b.forwardRef(({className:a,...o},u)=>m.jsx(cy,{ref:u,className:Te("-mx-1 my-1 h-px bg-muted",a),...o}));CE.displayName=cy.displayName;function DE({open:a,onClose:o,onSubmit:u,director:c}){const[s,d]=b.useState({name:"",din:"",designation:"",appointmentDate:""}),[h,v]=b.useState({}),y=N=>{if(!N)return"";const z=new Date(N);if(isNaN(z.getTime()))return N;const T=z.getDate().toString().padStart(2,"0"),L=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][z.getMonth()],k=z.getFullYear();return`${T}-${L}-${k}`},g=N=>{if(!N)return"";const z=N.split("-");if(z.length!==3)return"";const T=z[0],D=z[1],L=z[2],_=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"].indexOf(D);if(_===-1)return"";const Z=(_+1).toString().padStart(2,"0");return`${L}-${Z}-${T}`};b.useEffect(()=>{d(c?{name:c.name||"",din:c.din||"",designation:c.designation||"",appointmentDate:g(c.appointmentDate)||""}:{name:"",din:"",designation:"",appointmentDate:""}),v({})},[c,a]);const x=()=>{const N={};return s.name.trim()||(N.name="Name is required"),s.din.trim()||(N.din="DIN is required"),/^\d{8}$/.test(s.din)||(N.din="DIN must be 8 digits"),s.designation.trim()||(N.designation="Designation is required"),s.appointmentDate.trim()||(N.appointmentDate="Appointment date is required"),v(N),Object.keys(N).length===0},w=N=>{if(N.preventDefault(),x()){const z={...s,appointmentDate:y(s.appointmentDate)};u(z)}},R=()=>{d({name:"",din:"",designation:"",appointmentDate:""}),v({}),o()};return m.jsx(tg,{open:a,onOpenChange:R,children:m.jsxs(Hs,{className:"sm:max-w-[425px]",children:[m.jsx(Bs,{children:m.jsx(ks,{children:c?"Edit Director":"Add New Director"})}),m.jsxs("form",{onSubmit:w,className:"space-y-4",children:[m.jsxs("div",{className:"space-y-2",children:[m.jsx(zi,{htmlFor:"name",children:"Full Name *"}),m.jsx(vr,{id:"name",value:s.name,onChange:N=>d({...s,name:N.target.value}),placeholder:"Enter full name",className:h.name?"border-red-500":""}),h.name&&m.jsx("p",{className:"text-red-500 text-xs",children:h.name})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsx(zi,{htmlFor:"din",children:"DIN (Director Identification Number) *"}),m.jsx(vr,{id:"din",value:s.din,onChange:N=>d({...s,din:N.target.value}),placeholder:"8-digit DIN",maxLength:8,className:h.din?"border-red-500":""}),h.din&&m.jsx("p",{className:"text-red-500 text-xs",children:h.din})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsx(zi,{htmlFor:"designation",children:"Designation *"}),m.jsxs(AE,{value:s.designation,onValueChange:N=>d({...s,designation:N}),children:[m.jsx(uy,{className:h.designation?"border-red-500":"",children:m.jsx(NE,{placeholder:"Select designation"})}),m.jsxs(dy,{children:[m.jsx(Sa,{value:"Managing Director",children:"Managing Director"}),m.jsx(Sa,{value:"Director",children:"Director"}),m.jsx(Sa,{value:"Independent Director",children:"Independent Director"}),m.jsx(Sa,{value:"Executive Director",children:"Executive Director"}),m.jsx(Sa,{value:"Non-Executive Director",children:"Non-Executive Director"})]})]}),h.designation&&m.jsx("p",{className:"text-red-500 text-xs",children:h.designation})]}),m.jsxs("div",{className:"space-y-2",children:[m.jsx(zi,{htmlFor:"appointmentDate",children:"Appointment Date *"}),m.jsx(vr,{id:"appointmentDate",type:"date",value:s.appointmentDate,onChange:N=>d({...s,appointmentDate:N.target.value}),className:h.appointmentDate?"border-red-500":""}),h.appointmentDate&&m.jsx("p",{className:"text-red-500 text-xs",children:h.appointmentDate})]}),m.jsxs(Ls,{children:[m.jsx(kt,{type:"button",variant:"outline",onClick:R,children:"Cancel"}),m.jsxs(kt,{type:"submit",children:[c?"Update":"Add"," Director"]})]})]})]})})}function ME({open:a,onClose:o,onConfirm:u,title:c,description:s,itemName:d,itemDetails:h}){return m.jsx(tg,{open:a,onOpenChange:o,children:m.jsxs(Hs,{className:"sm:max-w-[425px]",children:[m.jsx(Bs,{children:m.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[m.jsx("div",{className:"p-3 bg-red-100 rounded-full",children:m.jsx(dr,{className:"w-6 h-6 text-red-600"})}),m.jsxs("div",{children:[m.jsx(ks,{className:"text-lg font-semibold text-gray-900",children:c}),m.jsx("p",{className:"text-gray-600",children:s})]})]})}),m.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:[m.jsxs("p",{className:"text-sm text-gray-700",children:["Are you sure you want to delete ",m.jsx("strong",{children:d}),"?"]}),h&&m.jsx("p",{className:"text-xs text-gray-500 mt-1",children:h})]}),m.jsxs(Ls,{children:[m.jsx(kt,{variant:"outline",onClick:o,children:"Cancel"}),m.jsx(kt,{variant:"destructive",onClick:u,children:"Delete"})]})]})})}function OE({directors:a,onAddDirector:o,onUpdateDirector:u,onDeleteDirector:c}){const[s,d]=b.useState(!1),[h,v]=b.useState(null),[y,g]=b.useState(!1),[x,w]=b.useState(null),R=_=>{v(_),d(!0)},N=_=>{w(_),g(!0)},z=_=>{h?u(h.id,_):o(_),d(!1),v(null)},T=()=>{d(!1),v(null)},D=()=>{x&&c(x.id),g(!1),w(null)},L=()=>{g(!1),w(null)},k=_=>{switch(_){case"Active":return"success";case"Inactive":return"secondary";default:return"secondary"}};return m.jsxs("div",{className:"space-y-6",children:[m.jsxs("div",{className:"flex items-center justify-between",children:[m.jsxs("div",{children:[m.jsx("h2",{className:"text-2xl font-bold text-foreground",children:"Directors"}),m.jsx("p",{className:"text-muted-foreground",children:"Manage company directors and their information"})]}),m.jsxs(kt,{onClick:()=>d(!0),className:"flex items-center gap-2",children:[m.jsx(Tx,{className:"w-4 h-4"}),"Add Director"]})]}),m.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[m.jsxs(Qt,{children:[m.jsxs(Zt,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[m.jsx(Kt,{className:"text-sm font-medium",children:"Total Directors"}),m.jsx($u,{className:"h-4 w-4 text-muted-foreground"})]}),m.jsxs(Jt,{children:[m.jsx("div",{className:"text-2xl font-bold",children:a.length}),m.jsx("p",{className:"text-xs text-muted-foreground",children:"Registered directors"})]})]}),m.jsxs(Qt,{children:[m.jsxs(Zt,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[m.jsx(Kt,{className:"text-sm font-medium",children:"Active Directors"}),m.jsx($u,{className:"h-4 w-4 text-green-600"})]}),m.jsxs(Jt,{children:[m.jsx("div",{className:"text-2xl font-bold text-green-600",children:a.filter(_=>_.status==="Active").length}),m.jsx("p",{className:"text-xs text-muted-foreground",children:"Currently active"})]})]}),m.jsxs(Qt,{children:[m.jsxs(Zt,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[m.jsx(Kt,{className:"text-sm font-medium",children:"Managing Directors"}),m.jsx($u,{className:"h-4 w-4 text-blue-600"})]}),m.jsxs(Jt,{children:[m.jsx("div",{className:"text-2xl font-bold text-blue-600",children:a.filter(_=>_.designation==="Managing Director").length}),m.jsx("p",{className:"text-xs text-muted-foreground",children:"Key executives"})]})]})]}),m.jsxs(Qt,{children:[m.jsx(Zt,{children:m.jsx(Kt,{children:"Directors List"})}),m.jsx(Jt,{children:m.jsxs(xp,{children:[m.jsx(Sp,{children:m.jsxs(ms,{children:[m.jsx(Al,{children:"Name"}),m.jsx(Al,{children:"DIN"}),m.jsx(Al,{children:"Designation"}),m.jsx(Al,{children:"Appointment Date"}),m.jsx(Al,{children:"Status"}),m.jsx(Al,{className:"text-right",children:"Actions"})]})}),m.jsx(wp,{children:a.map(_=>m.jsxs(ms,{children:[m.jsx(Nl,{className:"font-medium",children:_.name}),m.jsx(Nl,{className:"font-mono",children:_.din}),m.jsx(Nl,{children:_.designation}),m.jsx(Nl,{children:_.appointmentDate}),m.jsx(Nl,{children:m.jsx(ds,{variant:k(_.status),children:_.status})}),m.jsx(Nl,{className:"text-right",children:m.jsxs("div",{className:"flex items-center justify-end gap-2",children:[m.jsx(kt,{variant:"ghost",size:"icon",onClick:()=>R(_),children:m.jsx(xx,{className:"w-4 h-4"})}),m.jsx(kt,{variant:"ghost",size:"icon",onClick:()=>N(_),children:m.jsx(Cx,{className:"w-4 h-4"})})]})})]},_.id))})]})})]}),m.jsx(DE,{open:s,onClose:T,onSubmit:z,director:h}),m.jsx(ME,{open:y,onClose:L,onConfirm:D,title:"Delete Director",description:"This action cannot be undone.",itemName:x?.name||"",itemDetails:x?`DIN: ${x.din} • ${x.designation}`:""})]})}function _E(){const[a,o]=b.useState("overview"),u={cin:"U82990KA2023PTC182604",name:"Fractioned Technologies and Business Management Private Limited",status:"Active",category:"Company limited by shares",subcategory:"Non-govt company",registrationDate:"15-Dec-2023",authorizedCapital:"₹10,00,000",paidupCapital:"₹1,00,000",registrarOffice:"Karnataka",address:{street:"123, Technology Park, Electronic City",city:"Bangalore",state:"Karnataka",pincode:"560100"},contact:{phone:"+91-80-1234-5678",email:"<EMAIL>",website:"www.fractionedtech.com"}},[c,s]=b.useState([{id:"1",name:"Rajesh Kumar Sharma",din:"08765432",designation:"Managing Director",appointmentDate:"15-Dec-2023",status:"Active"},{id:"2",name:"Priya Srinivasan",din:"09876543",designation:"Director",appointmentDate:"15-Dec-2023",status:"Active"},{id:"3",name:"Amit Patel",din:"07654321",designation:"Independent Director",appointmentDate:"20-Jan-2024",status:"Active"}]),[d]=b.useState([{id:"1",name:"Annual Return Filing",dueDate:"30-Sep-2024",status:"Compliant",priority:"High",description:"Form MGT-7 filed successfully"},{id:"2",name:"Board Meeting Minutes",dueDate:"15-Nov-2024",status:"Pending",priority:"Medium",description:"Quarterly board meeting documentation pending"},{id:"3",name:"Income Tax Return",dueDate:"31-Oct-2024",status:"Overdue",priority:"High",description:"ITR filing is overdue - immediate action required"},{id:"4",name:"TDS Return Filing",dueDate:"07-Dec-2024",status:"Pending",priority:"Medium",description:"Monthly TDS return for November"},{id:"5",name:"Statutory Audit",dueDate:"30-Sep-2025",status:"Compliant",priority:"High",description:"Annual statutory audit completed"}]),h=x=>{const w={...x,id:Date.now().toString(),status:"Active"};s([...c,w])},v=(x,w)=>{s(c.map(R=>R.id===x?{...R,...w}:R))},y=x=>{s(c.filter(w=>w.id!==x))},g=()=>{switch(a){case"overview":return m.jsx(fv,{company:u,complianceItems:d});case"directors":return m.jsx(OE,{directors:c,onAddDirector:h,onUpdateDirector:v,onDeleteDirector:y});case"documents":return m.jsx("div",{children:"Documents page coming soon..."});case"compliance":return m.jsx("div",{children:"Compliance page coming soon..."});case"pending":return m.jsx("div",{children:"Pending tasks page coming soon..."});default:return m.jsx(fv,{company:u,complianceItems:d})}};return m.jsx(tS,{activeTab:a,onTabChange:o,children:g()})}Yb.createRoot(document.getElementById("root")).render(m.jsx(b.StrictMode,{children:m.jsx(_E,{})}));
